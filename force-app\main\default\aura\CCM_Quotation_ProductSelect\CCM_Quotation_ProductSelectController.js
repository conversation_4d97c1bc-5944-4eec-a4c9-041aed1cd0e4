({
    doInit: function (component, event, helper) {
        component.set('v.isBusy', true);
        component.set('v.currencySymbol', $A.get("$Locale.currencyCode"));
        component.set('v.isPortal', 'true');
        var orderItemListStr = JSON.stringify(component.get('v.orderItemList'));
        console.log('orderItemList----->aaaaaa', orderItemListStr);
        console.log('orderItemList----->', Boolean(orderItemListStr));
        if (orderItemListStr == '[]') {
            component.set('v.quotation.Total_Quantity__c', 0);
            component.set('v.quotation.Product_Price__c', 0.00);
            component.set('v.showFreeShippingMsg', false);
            component.set('v.isWaivedFreight', false);

            var promotionCode = helper.getUrlParameter('c__promotioncode');
            if (promotionCode) {
                helper.initalOrderItemByPromotion(component);
            }
        } else {
            var waivedFee = component.get('v.quotation.Freight_Fee_To_Be_Waived__c');
            waivedFee = isNaN(waivedFee) ? 0 : waivedFee;
            var freightTargetFee = component.get('v.quotation.Freight_Target_Fee__c');
            freightTargetFee = isNaN(freightTargetFee) ? 0 : freightTargetFee;
            if (freightTargetFee == 0) {
                component.set('v.showFreeShippingMsg', false);
            } else {
                if (waivedFee > 0) {
                    component.set('v.showFreeShippingMsg', true);
                    component.set('v.isWaivedFreight', false);
                } else {
                    component.set('v.showFreeShippingMsg', true);
                    component.set('v.isWaivedFreight', true);
                }
            }
            var quotationItemList = component.get("v.orderItemList");
			quotationItemList.forEach(function (pItem) {
				if (pItem.Lanch_Date__c > component.get("v.shipDate")) {
					pItem.shipDateGreaterLanuchDate = true;
				}
			});
            var recordId = helper.getUrlParameter('recordId');
            if (recordId) {
                helper.initialAvailablePromotion(component);
                helper.initialWholeOrderPromotion(component);
                helper.initialPaymentTermPromotion(component);
            }
        }

        if (component.get('v.brand')) {
            component.set('v.disableBtn', false);
            component.set("v.uploadDisabed", false);
        }
        component.set('v.paymentTermValue', component.get('v.quotation.Payment_Term__c'));
        console.log('payment term value 3--->' + component.get('v.paymentTermValue'));
        helper.generateCondtion(component, component.get('v.brand'));
        component.set('v.isBusy', false);
    },
    rowFocus: function (component, event, helper) {
        var indexRow = event.currentTarget.id;
        component.set('v.operationRow', indexRow);
    },
    addItem: function (component, event, helper) {
        var orderItemList = component.get('v.orderItemList');
        var newOrderItem =
        {
            "Product__c": '',
            "Brand__c": '',
            "Ship_Date__c":component.get('v.shipDate'),
            "previousQuantity__c": '1',
            "counterror": false,
            "Quantity__c": '1',
            "List_Price__c": '0.00',
            "Unit_Price__c": '0.00',
            "Sub_Total__c": '0.00',
            "promotionFilterCondition": '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Error"}]',
            "shipDateGreaterLanuchDate":false
        };
        orderItemList.push(newOrderItem);
        helper.generateCondtion(component, component.get('v.brand'));
        component.set('v.orderItemList', []);
        component.set('v.orderItemList', orderItemList);
    },
    onSelectProd: function (component, event, helper) {
        component.set('v.productPriceRefreshComplete', false);
        var productId = event.getSource().get('v.value');
        console.log('productId--->' + productId);
        if (!productId) {
            //如果没有product Id不需要调后台，不需要加loading效果
        } else {
            component.set('v.isBusy', true);
            var action = component.get("c.getPriceBook");
            action.setParams({
                "prodId": productId
            });
            action.setCallback(this, function (response) {
                var state = response.getState();
                console.log('state--->' + state);
                if (state === "SUCCESS") {
                    var results = response.getReturnValue();
                    if (results) {
                        var data = JSON.parse(results);
                        if (data) {
                            component.set('v.priceBookEntry', data.priceBookEntry);
                            component.set('v.product', data.product);
                            var orderItemList = component.get('v.orderItemList');
                            var index = component.get('v.operationRow');

                            if (data.OrgCode) {
                                component.set('v.OrgCode', data.OrgCode);
                            }
                            var orgcode = component.get('v.OrgCode');

                            if (data.product) {
                                orderItemList[index].Brand__c = data.product.Brand_Name__c;
                                orderItemList[index].ProductCode__c = data.product.ProductCode;
                                orderItemList[index].Gross_Weight__c = data.product.Weight__c == undefined ? 0.00 : data.product.Weight__c;
                                orderItemList[index].Is_Over_Size_Product__c = data.product.OverSize__c;
                                //Yanko
                                if (data.product.CS_Exchange_Rate__c && orgcode != 'CCA') {
                                    orderItemList[index].CS_Exchange_Rate__c = data.product.CS_Exchange_Rate__c;
                                    orderItemList[index].Quantity__c = orderItemList[index].CS_Exchange_Rate__c;
                                    orderItemList[index].previousQuantity__c = data.product.CS_Exchange_Rate__c;
                                } else {
                                    orderItemList[index].Quantity__c = 1;
                                    orderItemList[index].CS_Exchange_Rate__c = 1;
                                }
                                orderItemList[index].Lanch_Date__c = data.product.Lanch_Date__c;
                                if(orderItemList[index].Ship_Date__c < orderItemList[index].Lanch_Date__c) {
                                    orderItemList[index].shipDateGreaterLanuchDate = true;
                                }else{
                                    orderItemList[index].shipDateGreaterLanuchDate = false;
                                 }
                                //calvin start
                                orderItemList[index].counterror = false;
                                console.log('index', index, component.get('v.model'));
                                if (component.get('v.model') == Number(index) + 1) {
                                    component.set('v.showcaseqtyalert', false);
                                }
                                // end
                                orderItemList[index].hasproduct__c = true;
                                //YankoEnd
                                console.log('Gross Weight--->' + orderItemList[index].Gross_Weight__c);
                                console.log('hasproduct__c ======= ', orderItemList[index].hasproduct__c);
                                component.set("v.aaa", orderItemList[index].hasproduct__c);
                            }
                            if (data.priceBookEntry){
                                orderItemList[index].Price_Book__c = data.priceBookEntry.Pricebook2Id;
                            }
                            var unitPrice = data.priceBookEntry.UnitPrice;
                            if (unitPrice) {
                                orderItemList[index].List_Price__c = unitPrice.toFixed(2);
                                orderItemList[index].Unit_Price__c = unitPrice.toFixed(2);
                                var quantity = orderItemList[index].Quantity__c;
                                var subtotal = unitPrice * quantity;
                                orderItemList[index].Sub_Total__c = subtotal.toFixed(2);
                            } else {
                                orderItemList[index].List_Price__c = 0.00;
                                orderItemList[index].Unit_Price__c = 0.00;
                                orderItemList[index].Sub_Total__c = 0.00;
                            }

                            //promotion info
                            orderItemList[index].Discount_Amount__c = 0.00;
                            orderItemList[index].Promo_Discount_Amount__c = 0.00;
                            orderItemList[index].Whole_Order_Promo_Discount_Amount__c = 0.00;

                            if (orderItemList[index].PromotionName__c) {
                                helper.removePromotionQuotationItem(orderItemList, index, component);
                                helper.clearPromotionStatus(orderItemList, index);
                            }

                            orderItemList[index].promotionList = data.promotionList;
                            if (data.promotionList && data.promotionList.length > 0) {
                                orderItemList[index].HasPromo = true;
                                var promotionCodesStr = '';
                                data.promotionList.forEach(function (pItem) {
                                    promotionCodesStr += '\'' + pItem.promotion.Promo_Code__c + '\',';
                                });
                                var strCodes = promotionCodesStr.slice(0, -1);
                                orderItemList[index].promotionFilterCondition = '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Open"},{"FieldName":"Promo_Code__c", "Condtion":"IN", "Value":"(' + strCodes + ')"}]';
                            } else {
                                orderItemList[index].HasPromo = false;
                                orderItemList[index].promotionFilterCondition = '[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Error"}]';
                            }

                            orderItemList[index].Is_Initial__c = true;
                            //component.set('v.orderItemList', orderItemList);
                        }
                    }
                } else {
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            component.set('v.isBusy', false);
                            console.log($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                        }
                    } else {
                        component.set('v.isBusy', false);
                        console.log($A.get("$Label.c.CCM_Portal_ErrorTips"));
                    }
                }

                //if a whole order promotion is activated, recheck the threshold rules of whole order promo.
                helper.checkWholeOrderPromotion(orderItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(orderItemList, component.get("v.termsPromo"), component);

                component.set('v.orderItemList', []);
                component.set('v.orderItemList', orderItemList);

                helper.calculateTotal(component);
                component.set('v.isBusy', false);
                component.set('v.productPriceRefreshComplete', true);
            });
            $A.enqueueAction(action);
        }
    },
    handleDeleteAndCalculate: function (component, event, helper) {
        var orderItemList = component.get('v.orderItemList');
        if (event.getParam('index')) {
            var index = Number(event.getParam('index')) - 1; //the index of array begins 0
            orderItemList.splice(index, 1);
            component.set('v.orderItemList', orderItemList);
        }
        if (event.getParam('calculatePriceAndQuantity')) {
            helper.calculateTotal(component);
        }
    },
    doSave: function (component, event, helper) {
        if (!helper.checkOrderQty(component)) {
            return;
        }
        helper.doSaveAction(component, event, true);
    },
    nextStep: function (component, event, helper) {
        if (!helper.checkOrderQty(component)) {
            return;
        }
        helper.doSaveAction(component, event, false);
    },
    cancel: function (component) {
        window.location.href = window.location.origin + '/s/orderinformation';
    },
    calculateSubTotal: function (component, event, helper) {
        console.log('update qty===================');
        var hasPopup = false;
        component.set('v.isBusy', true);
        var orderItemList = component.get('v.orderItemList');
        if (orderItemList && orderItemList.length > 0) {
            // var index = component.get('v.operationRow');
            var index = event.getSource().get('v.name');
            var qItem = orderItemList[index];
            //Yanko
            var unitPrice = orderItemList[index].List_Price__c;
            var exchangerate = orderItemList[index].CS_Exchange_Rate__c;
            var quantity = orderItemList[index].Quantity__c;
            // let _index = component.get('v.operationRow');
            var model = Number(index) + 1;
            if (Number(quantity) % 1 != 0) {
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Warning"),
                    "message": $A.get("$Label.c.CCM_Portal_quantitymustbeaninteger"),
                    "type": "Warning"
                });
                toastEvent.fire();
                orderItemList[index].Quantity__c = orderItemList[index].CS_Exchange_Rate__c;
            }

            //for buyqty
            var buyqty = 1;
            var productBuyQty = 1;

            if(orderItemList[index].Promotion){
                if(orderItemList[index].Promotion.ruleList){
                    for(var i=0; i<orderItemList[index].Promotion.ruleList.length; i++){
                        if(orderItemList[index].Promotion.ruleList[i] && orderItemList[index].Promotion.ruleList[i].thresholdList){
                            for(var j=0; j<orderItemList[index].Promotion.ruleList[i].thresholdList.length; j++){
                                // add haibo: 新增 Therthod 为By Quantity of Specific Product && product buyQty, 按 product buyQty成倍修改数量（应用promotion时）
                                for(var k=0; k<orderItemList[index].Promotion.ruleList[i].thresholdList[j].products.length; k++){
                                    //BUY QTY promotion如果是price Discount 或者 Price Break，查找BuyQty
                                    if(orderItemList[index].Promotion.promotion.Promotion_Type__c == "Price Discount" || orderItemList[index].Promotion.promotion.Promotion_Type__c == "Price Break"){
                                        if(orderItemList[index].Promotion.ruleList[i].thresholdList[j]
                                            && ['By_Quantity_of_Specific_Product', 'By_Amount_of_Specific_Product'].includes(orderItemList[index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName)
                                            && orderItemList[index].Promotion.ruleList[i].thresholdList[j].products[0].Product__c === orderItemList[index].Product__c
                                        ){
                                            buyqty = orderItemList[index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                        }
                                    }
                                    if(orderItemList[index].Promotion.ruleList[i].thresholdList[j]
                                        && (orderItemList[index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'
                                        && orderItemList[index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c)){
                                            if (orderItemList[index].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == qItem.Product__c) {
                                                // 根据promotion type获取不同的buy qty
                                                if (orderItemList[index].Promotion.promotion.Promotion_Type__c == 'Price Discount') {
                                                    productBuyQty = orderItemList[index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                                } else {
                                                    productBuyQty = orderItemList[index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c;
                                                }
                                            }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if(buyqty && buyqty != 0 && buyqty != 1 && Number(productBuyQty) == 1){
                //for buyqty
                if(orderItemList[index].Quantity__c % Number(buyqty) != 0){
                    for(var a = 0; a < buyqty; a++){
                        if((Number(orderItemList[index].Quantity__c) + a) % Number(buyqty) == 0){
                            orderItemList[index].Quantity__c = Number(orderItemList[index].Quantity__c) + a;
                        }
                    }
                }
            } else if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
                if(Number(orderItemList[index].Quantity__c) % Number(productBuyQty) != 0){
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Warning!",
                        "message": `This product must be ordered by buy qty (${productBuyQty}) if you apply this promotion.`,
                        "type": "Warning"
                    });
                    toastEvent.fire();
                    // productBuyQty向上取整
                    orderItemList[index].Quantity__c = Math.ceil(Number(orderItemList[index].Quantity__c) / Number(productBuyQty)) * Number(productBuyQty);
                }
            }

            var woqty = orderItemList[index].Quantity__c;
            for (i = 0; i < orderItemList.length; i++) {
                if (orderItemList[i].ProductCode__c == orderItemList[index].ProductCode__c) {
                    if (i != index) {
                        var woqty = Number(woqty) + Number(orderItemList[i].Quantity__c);
                        var needcheck = 1;
                    }
                }
            }
            if (needcheck == 1) {
                var promremainder = Number(woqty) % Number(orderItemList[index].CS_Exchange_Rate__c);
            } else {
                var promremainder = Number(orderItemList[index].Quantity__c) % Number(orderItemList[index].CS_Exchange_Rate__c);
            }
            if (promremainder != 0) {
                var needcheckqty = false;
                var notonlyfreegoods = false;
                for (var x = 0; x < orderItemList.length; x++) {
                    if (orderItemList[x].Unit_Price__c != 0 && orderItemList[x].Product__c == orderItemList[index].Product__c) {
                        var notonlyfreegoods = true;
                        if (orderItemList[x].Promotion) {
                            if (orderItemList[x].Promotion.ruleList) {
                                for (var i = 0; i <= orderItemList[x].Promotion.ruleList.length; i++) {
                                    if (orderItemList[x].Promotion.ruleList[i] && orderItemList[x].Promotion.ruleList[i].thresholdList) {
                                        for (var j = 0; j <= orderItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
                                            if (orderItemList[x].Promotion.ruleList[i].thresholdList[j] && orderItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
                                                for (var k = 0; k <= orderItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
                                                    if (orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                        && orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == orderItemList[x].Product__c) {
                                                        var multiplecontrol = orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                        // var meetminqty = orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                        if (multiplecontrol == 'Inner Box Multiple') {
                                                            var needcheckqty = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            var needcheckqty = true;
                        }
                    }
                }

                if (notonlyfreegoods == false) {
                    var needcheckfreegoodsqty = false;
                    for (var x = 0; x < orderItemList.length; x++) {
                        if (orderItemList[x].Unit_Price__c == 0.00 && orderItemList[x].Promotion && orderItemList[x].Product__c == orderItemList[index].Product__c) {
                            if (orderItemList[x].Promotion.ruleList) {
                                for (var i = 0; i <= orderItemList[x].Promotion.ruleList.length; i++) {
                                    if (orderItemList[x].Promotion.ruleList[i] && orderItemList[x].Promotion.ruleList[i].offeringList) {
                                        for (var j = 0; j <= orderItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
                                            if (orderItemList[x].Promotion.ruleList[i].offeringList[j] && orderItemList[x].Promotion.ruleList[i].offeringList[j].products) {
                                                for (var k = 0; k <= orderItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
                                                    if (orderItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && orderItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                        && orderItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == orderItemList[x].Product__c) {
                                                        var multiplecontrol = orderItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                        // var meetminqty = orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                        if (multiplecontrol == 'Inner Box Multiple') {
                                                            var needcheckfreegoodsqty = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (needcheckfreegoodsqty == true) {
                        for (var i = 0; i < orderItemList.length; i++) {
                            if (orderItemList[i].ProductCode__c == orderItemList[index].ProductCode__c) {
                                orderItemList[i].counterror = true;
                            }
                        }
                    }
                } else {
                    if (needcheckqty == true) {
                        for (var i = 0; i < orderItemList.length; i++) {
                            if (orderItemList[i].ProductCode__c == orderItemList[index].ProductCode__c) {
                                orderItemList[i].counterror = true;
                            }
                        }
                    }
                }
            } else {
                orderItemList[index].counterror = false;
                for (var i = 0; i < orderItemList.length; i++) {
                    if (orderItemList[i].ProductCode__c == orderItemList[index].ProductCode__c) {
                        orderItemList[i].counterror = false;
                    }
                }
            }

            var subtotal = Number(orderItemList[index].List_Price__c) * Number(orderItemList[index].Quantity__c);
            orderItemList[index].Sub_Total__c = subtotal.toFixed(2);
            // change0227 没有promotion情况下 加减会错误更新subtotal
            if (orderItemList[index].Whole_Order_Promotion__c) {
                helper.checkWholeOrderPromotion(orderItemList, component.get("v.wholeOrderPromo"), component);
            }
            helper.calculateTotal(component);
            component.set('v.orderItemList', orderItemList);

            if (orderItemList[index].Promotion__c) {
                var hasRemoveItem = false;
                var promotion = orderItemList[index].Promotion;
                var ruleName = orderItemList[index].Promotion_Rule_Name__c;
                if (promotion && (orderItemList[index].HasPromo || orderItemList[index].isThreshold)) {
                    var isMeetThreshold = helper.isMeetThreshold(orderItemList, index, promotion);
                    if (promotion.promotion.Promotion_Type__c == 'BOGO'
                        || promotion.promotion.Promotion_Type__c == 'Full Pallet Promo') {
                        if (isMeetThreshold) {
                            helper.updateThresholdItem(orderItemList, index, promotion);
                            helper.updateOfferingItems(orderItemList, index, promotion);
                        } else {
                            var initialIndex = helper.getInitailIndex(orderItemList, ruleName);
                            helper.removePromotionQuotationItem(orderItemList, index, component);
                            helper.clearPromotionStatus(orderItemList, initialIndex);
                            orderItemList[initialIndex].Sub_Total__c = Number(orderItemList[initialIndex].List_Price__c * orderItemList[initialIndex].Quantity__c) + Number(orderItemList[initialIndex].Discount_Amount__c) + Number(orderItemList[initialIndex].Promo_Discount_Amount__c) + Number(orderItemList[initialIndex].Whole_Order_Promo_Discount_Amount__c);
                            orderItemList[initialIndex].Unit_Price__c = orderItemList[initialIndex].List_Price__c;
                            hasRemoveItem = true;
                        }
                    } else if (promotion.promotion.Promotion_Type__c == 'Price Break') {
                        if (isMeetThreshold) {
                            var oldRuleName = orderItemList[index].Promotion_Rule_Name__c;
                            var avilableRuleItem = helper.getAvailableRuleInPriceBreak(orderItemList, index, promotion);
                            var newRuleName = avilableRuleItem.ruleName;
                            if (oldRuleName == newRuleName) {
                                helper.updateThresholdItem(orderItemList, index, promotion);
                                helper.updateOfferingItems(orderItemList, index, promotion);
                            } else {
                                if (helper.isPoolFreeGoods(avilableRuleItem)) {
                                    helper.clearSelectedProducts(promotion);
                                    var offeringOpts = [];
                                    avilableRuleItem.offeringList.forEach(function (oItem) {
                                        if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                                            offeringOpts.push(oItem);
                                        }
                                    });

                                    orderItemList[index].hasPool = true;
                                    component.set('v.offeringOpts', offeringOpts);
                                    component.set('v.selectedPromotion', promotion);
                                    component.set("v.showOfferingProduct", true);
                                    hasPopup = true;
                                    component.set('v.editRow', index);
                                    component.set('v.isBusy', false);
                                    return;
                                } else {
                                    helper.removePromotionQuotationItem(orderItemList, index, component);
                                    var indexObj = {
                                        "startindex": 0
                                    };
                                    orderItemList[index].hasPool = false;
                                    helper.addThresholdItems(orderItemList, index, indexObj, component);
                                    helper.updateThresholdItem(orderItemList, index, promotion);
                                    helper.addOfferingItems(orderItemList, index, promotion, indexObj, component);
                                    console.log("controller 322 RUN addOffering");
                                    hasRemoveItem = true;
                                }
                            }
                        } else {
                            helper.removePromotionQuotationItem(orderItemList, index, component);
                            helper.clearPromotionStatus(orderItemList, index);
                            hasRemoveItem = true;
                        }
                    } else if (promotion.promotion.Promotion_Type__c == 'Price Discount') {
                        if (isMeetThreshold) {
                            helper.updateThresholdItem(orderItemList, index, promotion);
                        } else {
                            var initialIndex = helper.getInitailIndex(orderItemList, ruleName);
                            helper.removePromotionQuotationItem(orderItemList, index, component);
                            helper.clearPromotionStatus(orderItemList, initialIndex);
                            orderItemList[initialIndex].Sub_Total__c = Number(orderItemList[initialIndex].List_Price__c * orderItemList[initialIndex].Quantity__c) + Number(orderItemList[initialIndex].Discount_Amount__c) + Number(orderItemList[initialIndex].Promo_Discount_Amount__c) + Number(orderItemList[initialIndex].Whole_Order_Promo_Discount_Amount__c);
                            orderItemList[initialIndex].Unit_Price__c = orderItemList[initialIndex].List_Price__c;
                            hasRemoveItem = true;
                        }
                    } else if (promotion.promotion.Promotion_Type__c == 'Mix & Match') {
                        if (isMeetThreshold) {
                            helper.updateThresholdItemMixMatch(orderItemList, index, promotion);
                            helper.updateOfferingItems(orderItemList, index, promotion);
                        } else {
                            orderItemList.forEach(function (qItem) {
                                if (qItem.Promotion_Rule_Name__c == ruleName
                                    && qItem.isMix) {
                                    qItem.isMeet = false;
                                    qItem.Promo_Discount_Amount__c = 0.00;
                                    if (qItem.Quantity__c && qItem.Quantity__c > 0) {
                                        qItem.Sub_Total__c = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Discount_Amount__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Whole_Order_Promo_Discount_Amount__c);
                                        qItem.Unit_Price__c = Number((qItem.Sub_Total__c / qItem.Quantity__c)).toFixed(2);
                                    } else {
                                        qItem.Sub_Total__c = 0.00;
                                        qItem.Unit_Price__c = qItem.List_Price__c;
                                    }
                                }
                            });
                            helper.removeOfferingItemsOutOfPool(orderItemList, index, promotion);
                        }
                    } else if (promotion.promotion.Promotion_Type__c == 'Others') {
                        if (isMeetThreshold) {
                            if (helper.hasMixMatch(promotion)) {
                                helper.updateThresholdItemMixMatch(orderItemList, index, promotion);
                            }
                            else {
                                helper.updateThresholdItem(orderItemList, index, promotion);
                            }
                            helper.updateOfferingItems(orderItemList, index, promotion);
                        } else {
                            if (helper.hasMixMatch(promotion)) {
                                orderItemList.forEach(function (qItem) {
                                    if (qItem.Promotion_Rule_Name__c == ruleName
                                        && (qItem.Is_Initial__c || qItem.isThreshold)) {
                                        qItem.Promo_Discount_Amount__c = 0.00;
                                        if (qItem.Quantity__c && qItem.Quantity__c > 0) {
                                            qItem.Sub_Total__c = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Discount_Amount__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Whole_Order_Promo_Discount_Amount__c);
                                            qItem.Unit_Price__c = Number((qItem.Sub_Total__c / qItem.Quantity__c)).toFixed(2);
                                        } else {
                                            qItem.Sub_Total__c = 0.00;
                                            qItem.Unit_Price__c = qItem.List_Price__c;
                                        }

                                        if (qItem.isMix) {
                                            qItem.isMeet = false;
                                        }
                                    }
                                });
                                helper.removeOfferingItemsOutOfPool(orderItemList, index, promotion);
                            } else {
                                var initialIndex = helper.getInitailIndex(orderItemList, ruleName);
                                helper.removePromotionQuotationItem(orderItemList, index, component);
                                helper.clearPromotionStatus(orderItemList, initialIndex);
                                hasRemoveItem = true;
                            }
                        }
                    }
                } else if (promotion && orderItemList[index].isOffering && orderItemList[index].isPool) {
                    orderItemList[index].Promo_Discount_Amount__c = Number(orderItemList[index].List_Price__c * orderItemList[index].Quantity__c) * -1;
                    orderItemList[index].Sub_Total__c = 0.00;
                }

                if (hasPopup) {
                    return;
                }

                if (!hasRemoveItem) {
                    if (promotion && orderItemList[index].Promotion) {
                        var ruleItem = promotion.ruleList[0];
                        if (promotion.promotion.Promotion_Type__c == 'Price Break') {
                            var avilableRuleItem = helper.getAvailableRuleInPriceBreak(orderItemList, index, promotion);
                            if (avilableRuleItem) {
                                ruleItem = avilableRuleItem;
                            }
                        }
                        if (helper.isPoolFreeGoods(ruleItem)) {
                            helper.checkMeetOfferingPoolLimit(orderItemList, index, promotion);
                        }
                    }
                }


                helper.checkWholeOrderPromotion(orderItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(orderItemList, component.get("v.termsPromo"), component);

                if (hasRemoveItem) {
                    if (component.get('v.model') == Number(index) + 1) {
                        component.set('v.showcaseqtyalert', false);
                    }
                    component.set('v.orderItemList', []);
                }
            }

            if (orderItemList[index].counterror == false) {
                for (var s = 0; s < orderItemList.length; s++) {
                    var woqty = orderItemList[s].Quantity__c;
                    for (j = 0; j < orderItemList.length; j++) {
                        if (orderItemList[j].ProductCode__c == orderItemList[s].ProductCode__c) {
                            if (j != s) {
                                var woqty = Number(woqty) + Number(orderItemList[j].Quantity__c);
                                var needcheck = 1;
                            }
                        }
                    }
                    if (needcheck == 1) {
                        var validateqty = Number(woqty) % Number(orderItemList[s].CS_Exchange_Rate__c);
                    } else {
                        var validateqty = Number(orderItemList[s].Quantity__c) % Number(orderItemList[s].CS_Exchange_Rate__c);
                    }

                    if (validateqty != 0) {
                        var needcheckqty = false;
                        var notonlyfreegoods = false;
                        for (var x = 0; x < orderItemList.length; x++) {
                            if (orderItemList[x].Unit_Price__c != 0 && orderItemList[x].Product__c == orderItemList[s].Product__c) {
                                var notonlyfreegoods = true;
                                if (orderItemList[x].Promotion) {
                                    if (orderItemList[x].Promotion.ruleList) {
                                        for (var i = 0; i <= orderItemList[x].Promotion.ruleList.length; i++) {
                                            if (orderItemList[x].Promotion.ruleList[i] && orderItemList[x].Promotion.ruleList[i].thresholdList) {
                                                for (var j = 0; j <= orderItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
                                                    if (orderItemList[x].Promotion.ruleList[i].thresholdList[j] && orderItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
                                                        for (var k = 0; k <= orderItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
                                                            if (orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                                && orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == orderItemList[x].Product__c) {
                                                                var multiplecontrol = orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                                // var meetminqty = orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                                if (multiplecontrol == 'Inner Box Multiple') {
                                                                    var needcheckqty = true;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    var needcheckqty = true;
                                }
                            }
                        }

                        if (notonlyfreegoods == false) {
                            var needcheckfreegoodsqty = false;
                            for (var x = 0; x < orderItemList.length; x++) {
                                if (orderItemList[x].Unit_Price__c == 0.00 && orderItemList[x].Promotion && orderItemList[x].Product__c == orderItemList[s].Product__c) {
                                    if (orderItemList[x].Promotion.ruleList) {
                                        for (var i = 0; i <= orderItemList[x].Promotion.ruleList.length; i++) {
                                            if (orderItemList[x].Promotion.ruleList[i] && orderItemList[x].Promotion.ruleList[i].offeringList) {
                                                for (var j = 0; j <= orderItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
                                                    if (orderItemList[x].Promotion.ruleList[i].offeringList[j] && orderItemList[x].Promotion.ruleList[i].offeringList[j].products) {
                                                        for (var k = 0; k <= orderItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
                                                            if (orderItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && orderItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                                && orderItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == orderItemList[x].Product__c) {
                                                                var multiplecontrol = orderItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                                // var meetminqty = orderItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                                if (multiplecontrol == 'Inner Box Multiple') {
                                                                    var needcheckfreegoodsqty = true;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            if (needcheckfreegoodsqty == true) {
                                orderItemList[s].counterror = true;
                            } else {
                                orderItemList[s].counterror = false;
                            }
                        } else {
                            if (needcheckqty == true) {
                                orderItemList[s].counterror = true;
                            } else {
                                orderItemList[s].counterror = false;
                            }
                        }
                    } else {
                        orderItemList[s].counterror = false;
                    }
                }
            }

        }//quotation
        component.set('v.orderItemList', orderItemList);
        console.log("Yanko List ===== ", orderItemList[index].counterror);
        helper.calculateTotal(component);
        component.set('v.isBusy', false);
    },
    handleDelete: function (component, event, helper) {
        component.set('v.isBusy', true);
        var orderItemList = component.get('v.orderItemList');
        var deleteIndex = component.get('v.operationRow');
        if (deleteIndex != undefined) {
            var recordId = orderItemList[deleteIndex].Id;
            var promotionCode = orderItemList[deleteIndex].PromotionName__c;
            var ruleName = orderItemList[deleteIndex].Promotion_Rule_Name__c;
            var recordIds = [];
            if (recordId) {
                if (promotionCode) {
                    orderItemList.forEach(function (qItem) {
                        if (qItem.Promotion_Rule_Name__c == ruleName && qItem.Id) {
                            recordIds.push(qItem.Id);
                        }
                    });
                } else {
                    recordIds.push(recordId);
                }
                var action = component.get("c.deleteQuotation");
                action.setParams({
                    "recordIds": recordIds
                });
                action.setCallback(this, function (response) {
                    var state = response.getState();
                    console.log('state--->' + state);
                    if (state === "SUCCESS") {
                        var results = response.getReturnValue();
                        if (results) {
                            // orderItemList.splice(deleteIndex, 1);
                            // component.set('v.orderItemList', orderItemList);
                            if (promotionCode) {
                                var index = orderItemList.length - 1;
                                for (; index >= 0; index--) {
                                    var qItem = orderItemList[index];
                                    if (qItem.Promotion_Rule_Name__c == ruleName) {
                                        orderItemList.splice(index, 1);
                                    }
                                }
                            } else {
                                orderItemList.splice(deleteIndex, 1);
                            }

                            helper.checkWholeOrderPromotion(orderItemList, component.get("v.wholeOrderPromo"), component);
                            helper.checkPaymentTermPromotion(orderItemList, component.get("v.termsPromo"), component);

                            component.set('v.orderItemList', []);
                            component.set('v.orderItemList', orderItemList);
                        }
                    } else {
                        var errors = response.getError();
                        if (errors) {
                            if (errors[0] && errors[0].message) {
                                component.set('v.isBusy', false);
                                alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                            }
                        } else {
                            component.set('v.isBusy', false);
                            alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                        }
                    }

                    helper.calculateTotal(component);
                });
                $A.enqueueAction(action);
            } else {
                // orderItemList.splice(deleteIndex, 1);
                // component.set('v.orderItemList', orderItemList);
                //delete related threshold and offering items
                if (promotionCode) {
                    var index = orderItemList.length - 1;
                    for (; index >= 0; index--) {
                        var qItem = orderItemList[index];
                        if (qItem.Promotion_Rule_Name__c == ruleName) {
                            orderItemList.splice(index, 1);
                            // calvin start
                            if (component.get('v.model') == Number(index) + 1) {
                                component.set('v.showcaseqtyalert', false);
                            }
                            // end
                        }
                    }
                } else {
                    orderItemList.splice(deleteIndex, 1);
                    // calvin start
                    console.log("ggg", component.get('v.model'), deleteIndex)
                    if (component.get('v.model') == Number(deleteIndex) + 1) {
                        component.set('v.showcaseqtyalert', false);
                    }
                    // end
                }

                helper.checkWholeOrderPromotion(orderItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(orderItemList, component.get("v.termsPromo"), component);
                component.set('v.orderItemList', []);
                component.set('v.orderItemList', orderItemList);
            }
        }
        component.set('v.isBusy', false);
        helper.calculateTotal(component);

        //删除item后做一次全局校验：
        var quotationItemList = component.get('v.orderItemList');
        for (var s = 0; s < quotationItemList.length; s++) {
            var woqty = quotationItemList[s].Quantity__c;
            for (j = 0; j < quotationItemList.length; j++) {
                if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
                    if (j != s) {
                        var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
                        var needcheck = 1;
                    }
                }
            }
            if (needcheck == 1) {
                var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
            } else {
                var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
            }

            if (validateqty != 0) {
                var needcheckqty = false;
                var notonlyfreegoods = false;
                for (var x = 0; x < quotationItemList.length; x++) {
                    if (quotationItemList[x].Unit_Price__c != 0.00 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
                        var notonlyfreegoods = true;
                        if (quotationItemList[x].Promotion) {
                            if (quotationItemList[x].Promotion.ruleList) {
                                for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                    if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
                                        for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
                                            if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
                                                for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
                                                    if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                        && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                        var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                        // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                        if (multiplecontrol == 'Inner Box Multiple') {
                                                            var needcheckqty = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            var needcheckqty = true;
                        }
                    }
                }

                if (notonlyfreegoods == false) {
                    var needcheckfreegoodsqty = false;
                    for (var x = 0; x < quotationItemList.length; x++) {
                        if (quotationItemList[x].Unit_Price__c == 0.00 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
                            if (quotationItemList[x].Promotion.ruleList) {
                                for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                    if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
                                        for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
                                            if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
                                                for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
                                                    if (quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                        && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                        var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                        // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                        if (multiplecontrol == 'Inner Box Multiple') {
                                                            var needcheckfreegoodsqty = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (needcheckfreegoodsqty == true) {
                        quotationItemList[s].counterror = true;
                    } else {
                        quotationItemList[s].counterror = false;
                    }
                } else {
                    if (needcheckqty == true) {
                        quotationItemList[s].counterror = true;
                    } else {
                        quotationItemList[s].counterror = false;
                    }
                }
            } else {
                quotationItemList[s].counterror = false;
            }
        }
        component.set('v.orderItemList', quotationItemList);

    },
    refreshFlag: function (component, event, helper) {
        var brand = component.get('v.brand');
        helper.generateCondtion(component, brand);
        if (brand) {
            component.set('v.disableBtn', false);
            component.set("v.uploadDisabed", false);
        } else {
            component.set('v.disableBtn', true);
        }
    },
    refreshShipDate:function(component,event,helper){
        var orderItemList = component.get('v.orderItemList');
        var shipDate = component.get('v.shipDate');
        if(orderItemList && orderItemList.length > 0){
            for(var i=0; i<orderItemList.length; i++){
                orderItemList[i].Ship_Date__c = shipDate;
                if(orderItemList[i].Ship_Date__c < orderItemList[i].Lanch_Date__c) {
                    orderItemList[i].shipDateGreaterLanuchDate = true;
                }else{
                    orderItemList[i].shipDateGreaterLanuchDate = false;
                }
            }
        }
        component.set('v.orderItemList', orderItemList);
    },
    rebindPayment: function (component, event, helper) {
        let strPreviousPaymentTermValue = component.get("v.paymentTermValue");
        let isCash = false;
        component.set('v.isBusy', true);
        component.set('v.needResearch', false);
        var brand = component.get('v.brand');
        var customerType = component.get('v.customerType');
        var customerCluster = component.get('v.customerCluster');
        var defaultPaymentTerm = component.get('v.defaultPaymentTerm');
        var defaultPaymentTermLabel = component.get('v.defaultPaymentTerm') + ' ' + component.get('v.paymentTermLabel');
        if (defaultPaymentTerm == 'NA032' || defaultPaymentTerm == 'CA019') {
            isCash = true;
        }
        var customerOrgCode = component.get('v.customerOrgCode');
        console.log('defaultPaymentTerm--->' + defaultPaymentTerm);
        let NAScope = brand == 'EGO' && (customerCluster == 'CNA-CG11' || customerCluster == 'CNA-CG10' || customerCluster == 'CNA-CG20') && defaultPaymentTerm == 'NA001' && customerOrgCode != 'CCA';
        let CAScope = brand == 'EGO' && customerOrgCode == 'CCA' && (customerCluster == 'CA-CG11' || customerCluster == 'CA-CG12' || customerCluster == 'CA-CG05');
        //Update by Abby on 6/29/2020 for the payment term table will only be applied to customers whose cluster is OPE direct dealer and brand is EGO
        if ((NAScope || CAScope) && !isCash) {
            /*if (brand == 'EGO' && customerType == 'Direct Dealer'){*/
            if (component.get('v.quotation.Payment_Term__c') != component.get('v.paymentTermValue')) {
                component.set('v.paymentTermValue', null);
            }

            var paymentTermOptions = component.get('v.paymentTermAllOpts');
            var totalAmt = component.get('v.quotation.Product_Price__c');
            var options = [];
            var isOver = false;
            paymentTermOptions.forEach(function (element) {
                if (element.criteriaTo != null) {
                    if (element.criteriaFrom <= totalAmt && element.criteriaTo > totalAmt) {
                        options.push({ value: element.value, label: element.label });
                    }
                } else {
                    if (element.criteriaFrom <= totalAmt) {
                        options.push({ value: element.value, label: element.label });
                        isOver = true;
                    }
                }
            });
            if (isOver) {
                 options.push({ value: defaultPaymentTerm, label: defaultPaymentTermLabel });
            }
            if (options != null && options.length > 0 && options != undefined) {
                component.set('v.needResearch', true);
                component.set('v.paymentTermSelectOpt', options);

                if (!component.get("v.termsPromo")) {
                    let objPaymentTerm = options.find(objItem => objItem !== undefined && objItem.value === strPreviousPaymentTermValue);
                    component.set("v.paymentTermValue", objPaymentTerm === undefined ? "" : objPaymentTerm.value);
                    if (options.length === 1) {
                        component.set("v.paymentTermValue", options[0].value);
                    }
                    if ($A.util.isEmpty(component.get("v.paymentTermValue"))) {
                        helper.showToast("Warning", $A.get("$Label.c.CCM_Payment_Term_Selection_Reminder"));
                    }
                } else {
                    component.set("v.needResearch", false);
                }
            }
            else {
                component.set("v.paymentTermValue", component.get("v.defaultPaymentTerm"));
            }
        } else {
            if (!component.get("v.termsPromo")) {
                component.set("v.paymentTermValue", component.get("v.defaultPaymentTerm"));
            }
            component.set('v.needResearch', false);
        }
        console.log('payment term value 2--->' + component.get('v.paymentTermValue'));
        component.set('v.isBusy', false);
    },
    onDisplayPromo: function (component, event, helper) {
        var index = component.get('v.operationRow');
        var targetId = index + 'Tip';
        var cmpTarget = document.getElementById(targetId);
        $A.util.removeClass(cmpTarget, 'hideTip');
        $A.util.addClass(cmpTarget, 'promotionTip');
    },
    onHidePromo: function (component, event, helper) {
        var index = component.get('v.operationRow');
        var targetId = index + 'Tip';
        var cmpTarget = document.getElementById(targetId);
        $A.util.removeClass(cmpTarget, 'promotionTip');
        $A.util.addClass(cmpTarget, 'hideTip');
    },
    onApplyPromo: function (component, event, helper) {
        // calvin start
        console.log("apply");
        component.set('v.showcaseqtyalert', false);
        // end
        var index = component.get('v.operationRow');
        var targetId = index + 'Tip';
        var cmpTarget = document.getElementById(targetId);
        $A.util.removeClass(cmpTarget, 'promotionTip');
        $A.util.addClass(cmpTarget, 'hideTip');
        var pIndex = event.getSource().get('v.name');
        var quotationItemList = component.get('v.orderItemList');
        var promotionList = quotationItemList[index].promotionList;
        var selectedPromotion = promotionList[pIndex];
        var qItem = quotationItemList[index];
        console.log(JSON.stringify(qItem), 'qItem==============');
        // add haibo
        console.log(JSON.stringify(quotationItemList), 'quotationItemList================');

        //the promotion has already been used.
        var isDuplicatePromo = helper.isDuplicatePromo(quotationItemList, selectedPromotion);
        if (isDuplicatePromo) {
            //the promotion has already been used.
            var toastEvent = $A.get("e.force:showToast");
            toastEvent.setParams({
                "title": $A.get("$Label.c.CCM_Portal_Warning"),
                "message": $A.get("$Label.c.CCM_Portal_ThePromotion") + ' ' + selectedPromotion.promotion.Promotion_Code_For_External__c + ' ' + $A.get("$Label.c.CCM_Portal_HasAlreadyBeenUsed"),
                "type": "Warning"
            });
            toastEvent.fire();
            return;
        }

        helper.clearSelectedProducts(selectedPromotion);
        component.set('v.selectedPromotion', selectedPromotion);
        quotationItemList[index].hasPool = helper.isPoolFreeGoods(selectedPromotion.ruleList[0]);
        if (quotationItemList[index].hasPool) {
            component.set('v.isPoolFreeGoods', true);
        } else {
            component.set('v.isPoolFreeGoods', false);
        }
        component.set('v.orderItemList', quotationItemList);

        if (selectedPromotion.promotion.Promotion_Type__c == 'BOGO') {
            quotationItemList[index].hasMix = false;
            quotationItemList[index].isMix = false;
            if (quotationItemList[index].hasPool) {
                var offeringOpts = [];
                selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                    if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                        offeringOpts.push(oItem);
                    }
                });
                component.set('v.offeringOpts', offeringOpts);
                component.set("v.showOfferingProduct", true);
                component.set('v.orderItemList', quotationItemList);
            } else {
                if (quotationItemList[index].PromotionName__c) {
                    helper.removePromotionQuotationItem(quotationItemList, index, component);
                }
                quotationItemList[index].Promotion = selectedPromotion;
                quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
                quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
                quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
                quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
                var indexObj = {
                    "startindex": 0
                };
                helper.addThresholdItems(quotationItemList, index, indexObj, component);
                helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                console.log("controller 667 RUN addOffering");
                helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                component.set('v.orderItemList', []);
                component.set('v.orderItemList', quotationItemList);
                helper.calculateTotal(component);
            }
        } else if (selectedPromotion.promotion.Promotion_Type__c == 'Mix & Match') {
            var thresholdOpts = selectedPromotion.ruleList[0].thresholdList;
            thresholdOpts.forEach(function (tItem) {
                var products = tItem.products;
                products.forEach(function (pItem) {
                    if (quotationItemList[index].ProductCode__c == pItem.Product__r.ProductCode) {
                        pItem.selected = true;
                    }
                });
            });
            component.set('v.thresholdOpts', thresholdOpts);

            if (quotationItemList[index].hasPool) {
                var offeringOpts = [];
                selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                    if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                        offeringOpts.push(oItem);
                    }
                });
                component.set('v.offeringOpts', offeringOpts);
            }

            component.set('v.showThresholdProduct', true);
            component.set('v.orderItemList', []);
            component.set('v.orderItemList', quotationItemList);
        } else if (selectedPromotion.promotion.Promotion_Type__c == 'Price Discount') {
            quotationItemList[index].hasMix = false;
            quotationItemList[index].isMix = false;
            if (quotationItemList[index].PromotionName__c) {
                helper.removePromotionQuotationItem(quotationItemList, index, component);
            }
            quotationItemList[index].Promotion = selectedPromotion;
            quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
            quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
            quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
            quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
            var indexObj = {
                "startindex": 0
            };
            helper.addThresholdItems(quotationItemList, index, indexObj, component);
            helper.updateThresholdItem(quotationItemList, index, selectedPromotion);

            helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
            helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

            component.set('v.orderItemList', []);
            component.set('v.orderItemList', quotationItemList);
            helper.calculateTotal(component);
        } else if (selectedPromotion.promotion.Promotion_Type__c == 'Price Break') {
            var ruleItem = helper.getMiniRuleInPriceBreak(selectedPromotion);
            var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
            if (avilableRuleItem) {
                ruleItem = avilableRuleItem;
            }

            var offeringOpts = [];
            ruleItem.offeringList.forEach(function (oItem) {
                if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                    offeringOpts.push(oItem);
                }
            });

            if (offeringOpts.length > 0) {
                quotationItemList[index].hasPool = true;
                quotationItemList[index].hasMix = false;
                quotationItemList[index].isMix = false;
                component.set('v.offeringOpts', offeringOpts);
                component.set("v.showOfferingProduct", true);
                component.set('v.orderItemList', quotationItemList);
            } else {
                quotationItemList[index].hasMix = false;
                quotationItemList[index].isMix = false;
                if (quotationItemList[index].PromotionName__c) {
                    helper.removePromotionQuotationItem(quotationItemList, index, component);
                    helper.clearPromotionStatus(quotationItemList, index);
                }
                quotationItemList[index].Promotion = selectedPromotion;
                quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
                quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
                quotationItemList[index].Promotion_Rule_Name__c = ruleItem.ruleName;
                quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
                var indexObj = {
                    "startindex": 0
                };
                helper.addThresholdItems(quotationItemList, index, indexObj, component);
                helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                console.log("controller 762 RUN addOffering");
                helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                component.set('v.orderItemList', []);
                component.set('v.orderItemList', quotationItemList);
                helper.calculateTotal(component);
            }
        } else if (selectedPromotion.promotion.Promotion_Type__c == 'Full Pallet Promo') {
            if (quotationItemList[index].hasPool) {
                var offeringOpts = [];
                selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                    if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                        offeringOpts.push(oItem);
                    }
                });
                component.set('v.offeringOpts', offeringOpts);
                component.set("v.showOfferingProduct", true);
                component.set('v.orderItemList', quotationItemList);
            } else {
                quotationItemList[index].hasMix = false;
                quotationItemList[index].isMix = false;
                if (quotationItemList[index].PromotionName__c) {
                    helper.removePromotionQuotationItem(quotationItemList, index, component);
                }
                quotationItemList[index].Promotion = selectedPromotion;
                quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
                quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
                quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
                quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
                var indexObj = {
                    "startindex": 0
                };
                helper.addThresholdItems(quotationItemList, index, indexObj, component);
                helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                console.log("controller 798 RUN addOffering");
                helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                component.set('v.orderItemList', []);
                component.set('v.orderItemList', quotationItemList);
                helper.calculateTotal(component);
            }
        } else if (selectedPromotion.promotion.Promotion_Type__c == 'Others') {
            var thresholdList = selectedPromotion.ruleList[0].thresholdList;
            var thresholdOpts = [];
            thresholdList.forEach(function (tItem) {
                if (tItem.threshold.RecordType.DeveloperName == 'By_Mix_Match') {
                    thresholdOpts.push(tItem);
                    var products = tItem.products;
                    products.forEach(function (pItem) {
                        if (quotationItemList[index].ProductCode__c == pItem.Product__r.ProductCode) {
                            pItem.selected = true;
                        }
                    });
                }

            });
            component.set('v.thresholdOpts', thresholdOpts);

            var offeringOpts = [];
            if (quotationItemList[index].hasPool) {
                selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                    if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                        offeringOpts.push(oItem);
                    }
                });
            }
            component.set('v.offeringOpts', offeringOpts);

            if (thresholdOpts.length > 0) {
                component.set('v.showThresholdProduct', true);
            } else if (offeringOpts.length > 0) {
                component.set('v.showOfferingProduct', true);
            } else {
                quotationItemList[index].hasMix = false;
                quotationItemList[index].isMix = false;
                if (quotationItemList[index].PromotionName__c) {
                    helper.removePromotionQuotationItem(quotationItemList, index, component);
                }
                quotationItemList[index].Promotion = selectedPromotion;
                quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
                quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
                quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
                quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
                var indexObj = {
                    "startindex": 0
                };
                helper.addThresholdItems(quotationItemList, index, indexObj, component);
                helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                console.log("controller 854 RUN addOffering");
                helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                component.set('v.orderItemList', []);
                component.set('v.orderItemList', quotationItemList);
                helper.calculateTotal(component);
            }
        }
    },
    onSelectPromo: function (component, event, helper) {
        var index = component.get('v.operationRow');
        var promotionId = event.getSource().get('v.value');
        var quotationItemList = component.get('v.orderItemList');
        if (!promotionId) {
            if (quotationItemList[index].Promotion__c) {
                helper.removePromotionQuotationItem(quotationItemList, index, component);
                var quotationItem = quotationItemList[index];
                quotationItem.hasPool = false;
                quotationItem.isPool = false;
                quotationItem.hasMix = false;
                quotationItem.isMix = false;
                quotationItem.isThreshold = false;
                quotationItem.isOffering = false;
                quotationItem.Promotion__c = undefined;
                quotationItem.Promotion = undefined;
                quotationItem.Promotion_Rule_Name__c = undefined;
                helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                component.set('v.orderItemList', []);
                component.set('v.orderItemList', quotationItemList);
                helper.calculateTotal(component);
            }
            return;
        }

        if (promotionId && quotationItemList[index].Promotion__c == promotionId) {
            return;
        }

        var promotionList = quotationItemList[index].promotionList;
        var selectedPromotion;
        var isAvailable = false;
        if (promotionList && promotionList.length > 0) {
            promotionList.forEach(function (pItem) {
                if (pItem.promotion.Id == promotionId) {
                    isAvailable = true;
                    selectedPromotion = pItem;
                }
            });
        } else {
            isAvailable = false;
        }
        if (isAvailable) {
            var isDuplicatePromo = helper.isDuplicatePromo(quotationItemList, selectedPromotion);
            if (isDuplicatePromo) {
                //the promotion has already been used.
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Warning"),
                    "message": $A.get("$Label.c.CCM_Portal_Error") + ": " + $A.get("$Label.c.CCM_Portal_ThePromotion") + ' ' + selectedPromotion.promotion.Promotion_Code_For_External__c + ' ' + $A.get("$Label.c.CCM_Portal_HasAlreadyBeenUsed"),
                    "type": "Warning"
                });
                toastEvent.fire();

                if (quotationItemList[index].Promotion__c) {
                    promotionList.forEach(function (pItem) {
                        if (pItem.promotion.Id == quotationItemList[index].Promotion__c) {
                            quotationItemList[index].PromotionName__c = pItem.promotion.Promotion_Code_For_External__c;
                        }
                    });
                } else {
                    quotationItemList[index].PromotionName__c = undefined;
                }
                component.set('v.orderItemList', quotationItemList);
                return;
            }

            if (quotationItemList[index].Promotion__c && quotationItemList[index].Promotion__c != selectedPromotion.promotion.Id) {
                helper.removePromotionQuotationItem(quotationItemList, index, component);
                var quotationItem = quotationItemList[index];
                quotationItem.hasPool = false;
                quotationItem.isPool = false;
                quotationItem.hasMix = false;
                quotationItem.isMix = false;
                quotationItem.isThreshold = false;
                quotationItem.isOffering = false;
                quotationItem.Promotion__c = undefined;
                quotationItem.Promotion = undefined;
                quotationItem.Promotion_Rule_Name__c = undefined;
            }
            quotationItemList[index].Promotion = selectedPromotion;
            quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
            quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
            quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;

            helper.clearSelectedProducts(selectedPromotion);
            component.set('v.selectedPromotion', selectedPromotion);
            quotationItemList[index].hasPool = helper.isPoolFreeGoods(selectedPromotion.ruleList[0]);
            if (quotationItemList[index].hasPool) {
                component.set('v.isPoolFreeGoods', true);
            } else {
                component.set('v.isPoolFreeGoods', false);
            }
            component.set('v.orderItemList', quotationItemList);

            if (selectedPromotion.promotion.Promotion_Type__c == 'BOGO') {
                quotationItemList[index].hasMix = false;
                quotationItemList[index].isMix = false;
                if (quotationItemList[index].hasPool) {
                    var offeringOpts = [];
                    selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                        if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                            offeringOpts.push(oItem);
                        }
                    });
                    component.set('v.offeringOpts', offeringOpts);
                    component.set("v.showOfferingProduct", true);
                    component.set('v.orderItemList', quotationItemList);
                } else {
                    var indexObj = {
                        "startindex": 0
                    };
                    helper.addThresholdItems(quotationItemList, index, indexObj, component);
                    helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                    console.log("controller 980 RUN addOffering");
                    helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                    helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                    component.set('v.orderItemList', []);
                    component.set('v.orderItemList', quotationItemList);
                    helper.calculateTotal(component);
                }
            } else if (selectedPromotion.promotion.Promotion_Type__c == 'Mix & Match') {
                var thresholdOpts = selectedPromotion.ruleList[0].thresholdList;
                if (thresholdOpts.length > 1) {
                    component.set('v.isMultiThresholdProduct', true);
                } else {
                    component.set('v.isMultiThresholdProduct', false);
                }
                thresholdOpts.forEach(function (tItem) {
                    var products = tItem.products;
                    products.forEach(function (pItem) {
                        if (quotationItemList[index].ProductCode__c == pItem.Product__r.ProductCode) {
                            pItem.selected = true;
                        }
                    });
                });
                component.set('v.thresholdOpts', thresholdOpts);

                if (quotationItemList[index].hasPool) {
                    var offeringOpts = [];
                    selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                        if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                            offeringOpts.push(oItem);
                        }
                    });
                    component.set('v.offeringOpts', offeringOpts);
                }

                component.set('v.showThresholdProduct', true);
                component.set('v.orderItemList', []);
                component.set('v.orderItemList', quotationItemList);
            } else if (selectedPromotion.promotion.Promotion_Type__c == 'Price Discount') {
                quotationItemList[index].hasMix = false;
                quotationItemList[index].isMix = false;
                var indexObj = {
                    "startindex": 0
                };
                helper.addThresholdItems(quotationItemList, index, indexObj, component);
                helper.updateThresholdItem(quotationItemList, index, selectedPromotion);

                helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                component.set('v.orderItemList', []);
                component.set('v.orderItemList', quotationItemList);
                helper.calculateTotal(component);
            } else if (selectedPromotion.promotion.Promotion_Type__c == 'Price Break') {
                var ruleItem = helper.getMiniRuleInPriceBreak(selectedPromotion);
                var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
                if (avilableRuleItem) {
                    ruleItem = avilableRuleItem;
                }
                var offeringOpts = [];
                ruleItem.offeringList.forEach(function (oItem) {
                    if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                        offeringOpts.push(oItem);
                    }
                });

                if (offeringOpts.length > 0) {
                    quotationItemList[index].hasPool = true;
                    component.set('v.offeringOpts', offeringOpts);
                    component.set("v.showOfferingProduct", true);
                    component.set('v.orderItemList', quotationItemList);
                } else {
                    quotationItemList[index].hasMix = false;
                    quotationItemList[index].isMix = false;
                    var indexObj = {
                        "startindex": 0
                    };
                    helper.addThresholdItems(quotationItemList, index, indexObj, component);
                    helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                    helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                    console.log("controller 1059 RUN addOffering");
                    helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                    helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                    component.set('v.orderItemList', []);
                    component.set('v.orderItemList', quotationItemList);
                    helper.calculateTotal(component);
                }
            } else if (selectedPromotion.promotion.Promotion_Type__c == 'Full Pallet Promo') {
                if (quotationItemList[index].hasPool) {
                    var offeringOpts = [];
                    selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                        if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                            offeringOpts.push(oItem);
                        }
                    });
                    component.set('v.offeringOpts', offeringOpts);
                    component.set("v.showOfferingProduct", true);
                    component.set('v.orderItemList', quotationItemList);
                } else {
                    quotationItemList[index].hasMix = false;
                    quotationItemList[index].isMix = false;
                    var indexObj = {
                        "startindex": 0
                    };
                    helper.addThresholdItems(quotationItemList, index, indexObj, component);
                    helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                    helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                    console.log("controller 1088 RUN addOffering");
                    helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                    helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                    component.set('v.orderItemList', []);
                    component.set('v.orderItemList', quotationItemList);
                    helper.calculateTotal(component);
                }
            } else if (selectedPromotion.promotion.Promotion_Type__c == 'Others') {
                var thresholdList = selectedPromotion.ruleList[0].thresholdList;
                var thresholdOpts = [];
                thresholdList.forEach(function (tItem) {
                    if (tItem.threshold.RecordType.DeveloperName == 'By_Mix_Match') {
                        thresholdOpts.push(tItem);
                        var products = tItem.products;
                        products.forEach(function (pItem) {
                            if (quotationItemList[index].ProductCode__c == pItem.Product__r.ProductCode) {
                                pItem.selected = true;
                            }
                        });
                    }

                });
                component.set('v.thresholdOpts', thresholdOpts);

                var offeringOpts = [];
                if (quotationItemList[index].hasPool) {
                    selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                        if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                            offeringOpts.push(oItem);
                        }
                    });
                }
                component.set('v.offeringOpts', offeringOpts);

                if (thresholdOpts.length > 0) {
                    component.set('v.showThresholdProduct', true);
                } else if (offeringOpts.length > 0) {
                    component.set('v.showOfferingProduct', true);
                } else {
                    quotationItemList[index].hasMix = false;
                    quotationItemList[index].isMix = false;
                    if (quotationItemList[index].PromotionName__c) {
                        helper.removePromotionQuotationItem(quotationItemList, index, component);
                    }
                    quotationItemList[index].Promotion = selectedPromotion;
                    quotationItemList[index].PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
                    quotationItemList[index].Promotion__c = selectedPromotion.promotion.Id;
                    quotationItemList[index].Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
                    quotationItemList[index].Regular_Promotion_Window__c = selectedPromotion.windowId;
                    var indexObj = {
                        "startindex": 0
                    };
                    helper.addThresholdItems(quotationItemList, index, indexObj, component);
                    helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                    helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                    console.log("controller 1144 RUN addOffering");
                    helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                    helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                    component.set('v.orderItemList', []);
                    component.set('v.orderItemList', quotationItemList);
                    helper.calculateTotal(component);
                }
            }
        } else {
            var toastEvent = $A.get("e.force:showToast");
            toastEvent.setParams({
                "title": $A.get("$Label.c.CCM_Portal_Warning"),
                "message": $A.get("$Label.c.CCM_Portal_Thispromotioncodeisnotavailableforthecustomer"),
                "type": "Warning"
            });
            toastEvent.fire();
            if (quotationItemList[index].Promotion__c) {
                promotionList.forEach(function (pItem) {
                    if (pItem.promotion.Id == quotationItemList[index].Promotion__c) {
                        quotationItemList[index].PromotionName__c = pItem.promotion.Promotion_Code_For_External__c;
                    }
                });
            } else {
                quotationItemList[index].PromotionName__c = undefined;
            }
            component.set('v.orderItemList', quotationItemList);
        }
    },
    navigateToPromotion: function (component, event, helper) {
        var windowId = event.currentTarget.name;
        let hostname = window.location.hostname;
        var url = '/s/promotion-detail?c__windowid=' + windowId;
        window.open(url, '_blank');
    },
    closeThresholdProduct: function (component, event, helper) {
        component.set("v.showThresholdProduct", false);
        var index = component.get('v.operationRow');
        var quotationItemList = component.get('v.orderItemList');
        var quotationItem = quotationItemList[index];
        if (!quotationItem.Promotion) {
            quotationItem.hasMix = false;
            quotationItem.isMix = false;
            quotationItem.hasPool = false;
            component.set('v.orderItemList', quotationItemList);
        } else {
            var selectedPromotion = quotationItem.Promotion;
            if (!helper.hasMixMatch(selectedPromotion)) {
                quotationItem.hasMix = false;
                quotationItem.isMix = false;
            }
            quotationItem.hasPool = helper.isPoolFreeGoods(selectedPromotion.ruleList[0]);
            var selectedThresholdProducts = component.get('v.oldSelectedThresholdProducts');
            var ruleName = quotationItem.Promotion.ruleList[0].ruleName;
            if (selectedThresholdProducts && selectedThresholdProducts.length > 0) {
                var thresholdOpts = selectedPromotion.ruleList[0].thresholdList;
                thresholdOpts.forEach(function (tItem) {
                    var products = tItem.products;
                    products.forEach(function (pItem) {
                        if (selectedThresholdProducts.indexOf(pItem.Product__r.ProductCode) != -1) {
                            pItem.selected = true;
                        } else {
                            pItem.selected = false;
                        }
                    });
                });
                component.set('v.oldSelectedThresholdProducts', []);
            }
            component.set('v.orderItemList', quotationItemList);
        }
    },
    finishThresholdProduct: function (component, event, helper) {
        var thresholdOpts = component.get('v.thresholdOpts');
        var isMeet = true;
        for (var i = 0; i < thresholdOpts.length; i++) {
            var tItem = thresholdOpts[i];
            var miniDiffModels = tItem.threshold.Minimum_Different_Tool_Models__c;
            var maxDiffModels = tItem.threshold.Max_Different_Tool_Models__c;
            var products = tItem.products;
            var selectedDiffModels = 0;
            products.forEach(function (pItem) {
                if (pItem.selected) {
                    selectedDiffModels++;
                }
            });
            if (selectedDiffModels < miniDiffModels) {
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Warning"),
                    "message": $A.get("$Label.c.CCM_Portal_Pleaseselectatleast") + ' ' + miniDiffModels + ' ' + $A.get("$Label.c.CCM_Portal_DifferentModelsInMixMatchList") + (i + 1) + ".",
                    "type": "Warning"
                });
                toastEvent.fire();
                isMeet = false;
                break;
            }
            if (maxDiffModels && selectedDiffModels > maxDiffModels) {
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Warning"),
                    "message": $A.get("$Label.c.CCM_Portal_PleaseSelectAtMost") + maxDiffModels + ' ' + $A.get("$Label.c.CCM_Portal_DifferentModelsInMixMatchList") + (i + 1) + ".",
                    "type": "Warning"
                });
                toastEvent.fire();
                isMeet = false;
                break;
            }
        }

        if (!isMeet) {
            return;
        }

        component.set("v.showThresholdProduct", false);
        var index = component.get('v.operationRow');
        var quotationItemList = component.get('v.orderItemList');
        var quotationItem = quotationItemList[index];
        if (quotationItem.hasPool) {
            component.set("v.showOfferingProduct", true);
        } else {
            var selectedPromotion = component.get('v.selectedPromotion');
            quotationItem.hasMix = true;
            if (quotationItem.PromotionName__c) {
                helper.removePromotionQuotationItem(quotationItemList, index, component);
            }
            quotationItem.Promotion = selectedPromotion;
            quotationItem.PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
            quotationItem.Promotion__c = selectedPromotion.promotion.Id;
            quotationItem.Promotion_Rule_Name__c = selectedPromotion.ruleList[0].ruleName;
            quotationItem.Regular_Promotion_Window__c = selectedPromotion.windowId;
            var indexObj = {
                "startindex": 0
            };
            helper.addThresholdItems(quotationItemList, index, indexObj, component);
            if (helper.isMeetThreshold(quotationItemList, index, selectedPromotion)) {
                if (selectedPromotion.promotion.Promotion_Type__c == 'Mix & Match') {
                    helper.updateThresholdItemMixMatch(quotationItemList, index, selectedPromotion);
                }
                else {
                    if (helper.hasMixMatch(selectedPromotion)) {
                        helper.updateThresholdItemMixMatch(quotationItemList, index, selectedPromotion);
                    }
                    else {
                        helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                    }
                }
            }
            helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
            console.log("controller 1282 RUN addOffering");

            helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
            helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

            component.set('v.orderItemList', []);
            component.set('v.orderItemList', quotationItemList);
            helper.calculateTotal(component);
            component.set('v.oldSelectedThresholdProducts', []);
        }
    },
    handleThresholdSelection: function (component, event, helper) {
        var index = component.get('v.operationRow');
        var quotationItemList = component.get('v.orderItemList');
        var currentProduct = quotationItemList[index].Product__r.SF_Description__c;
        var item = event.currentTarget;
        if (item && item.dataset) {
            var value = item.dataset.value;
            var name = item.getAttribute('data-name');
            var selected = item.dataset.selected;
            var options = component.get("v.thresholdOpts");
            options.forEach(function (element) {
                var threshold = element.threshold;
                var products = element.products;
                if (threshold.Name == name) {
                    products.forEach(function (p) {
                        if (p.Product__r.SF_Description__c == currentProduct) {
                            p.selected = true;
                        } else if (p.Product__r.SF_Description__c == value) {
                            p.selected = p.selected ? false : true;
                        }
                    });
                }
            });
            component.set("v.thresholdOpts", options);
        }
    },
    closeOfferingProduct: function (component, event, helper) {
        component.set("v.showOfferingProduct", false);
        var index = component.get('v.operationRow');
        if (component.get('v.editRow') > -1) {
            index = component.get('v.editRow');
        }
        //var index = component.get('v.operationRow');
        var quotationItemList = component.get('v.orderItemList');
        var quotationItem = quotationItemList[index];
        if (!quotationItem.Promotion) {
            quotationItem.hasMix = false;
            quotationItem.isMix = false;
            quotationItem.hasPool = false;
            component.set('v.orderItemList', quotationItemList);
        } else {
            var selectedPromotion = quotationItem.Promotion;
            if (!helper.hasMixMatch(selectedPromotion)) {
                quotationItem.hasMix = false;
                quotationItem.isMix = false;
            }

            var ruleItem = selectedPromotion.ruleList[0];
            if (selectedPromotion.promotion.Promotion_Type__c == 'Price Break') {
                var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
                if (avilableRuleItem) {
                    ruleItem = avilableRuleItem;
                    var oldRuleName = quotationItem.Promotion_Rule_Name__c;
                    var newRuleName = avilableRuleItem.ruleName;
                    if (oldRuleName != newRuleName) {
                        helper.removePromotionQuotationItem(quotationItemList, index, component);
                        ruleItem.offeringList.forEach(function (oItem) {
                            if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                                var products = oItem.products;
                                products.forEach(function (pItem) {
                                    pItem.selected = false;
                                });
                            }
                        });
                        var indexObj = {
                            "startindex": 0
                        };
                        helper.addThresholdItems(quotationItemList, index, indexObj, component);
                        helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                        helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
                        console.log("controller 1362 RUN addOffering");
                        helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                        helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

                        component.set('v.orderItemList', []);
                        component.set('v.orderItemList', quotationItemList);
                        helper.calculateTotal(component);
                    }
                }
            }

            var offeringOpts = [];
            ruleItem.offeringList.forEach(function (oItem) {
                if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                    offeringOpts.push(oItem);
                }
            });
            if (offeringOpts.length > 0) {
                quotationItem.hasPool = true;
            } else {
                quotationItem.hasPool = false;
            }

            var selectedThresholdProducts = component.get('v.oldSelectedThresholdProducts');
            if (selectedThresholdProducts && selectedThresholdProducts.length > 0) {
                var thresholdOpts = ruleItem.thresholdList;
                thresholdOpts.forEach(function (tItem) {
                    var products = tItem.products;
                    products.forEach(function (pItem) {
                        if (selectedThresholdProducts.indexOf(pItem.Product__r.ProductCode) != -1) {
                            pItem.selected = true;
                        } else {
                            pItem.selected = false;
                        }
                    });
                });
                component.set('v.oldSelectedThresholdProducts', []);
            }
            var selectedOfferingProducts = component.get('v.oldSelectedOfferingProducts');
            if (selectedOfferingProducts && selectedOfferingProducts.length > 0) {
                var offeringOpts = ruleItem.offeringList;;
                offeringOpts.forEach(function (oItem) {
                    if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                        var products = oItem.products;
                        products.forEach(function (pItem) {
                            if (selectedOfferingProducts.indexOf(pItem.Product__r.ProductCode) != -1) {
                                pItem.selected = true;
                            } else {
                                pItem.selected = false;
                            }
                        });
                    }
                });
                component.set('v.oldSelectedOfferingProducts', []);
            }
            component.set('v.orderItemList', quotationItemList);
        }
        component.set('v.editRow', -1);
    },
    finishOfferingProduct: function (component, event, helper) {
        component.set("v.showOfferingProduct", false);
        var selectedPromotion = component.get('v.selectedPromotion');
        var index = component.get('v.operationRow');
        if (component.get('v.editRow') > -1) {
            index = component.get('v.editRow');
        }
        var quotationItemList = component.get('v.orderItemList');
        var indexObj = {
            "startindex": 0
        };
        var quotationItem = quotationItemList[index];
        if (quotationItem.PromotionName__c) {
            helper.removePromotionQuotationItem(quotationItemList, index, component);
        }
        quotationItem.Promotion = selectedPromotion;
        quotationItem.PromotionName__c = selectedPromotion.promotion.Promotion_Code_For_External__c;
        quotationItem.Promotion__c = selectedPromotion.promotion.Id;
        quotationItem.Regular_Promotion_Window__c = selectedPromotion.windowId;

        var ruleItem = selectedPromotion.ruleList[0];
        if (selectedPromotion.promotion.Promotion_Type__c == 'Price Break') {
            var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
            if (avilableRuleItem) {
                ruleItem = avilableRuleItem;
            }
        }
        quotationItem.Promotion_Rule_Name__c = ruleItem.ruleName;

        if (helper.hasMixMatch(selectedPromotion)) {
            quotationItem.hasMix = true;
        }
        helper.addThresholdItems(quotationItemList, index, indexObj, component);
        if (helper.isMeetThreshold(quotationItemList, index, selectedPromotion)) {
            if (selectedPromotion.promotion.Promotion_Type__c == 'Mix & Match') {
                helper.updateThresholdItemMixMatch(quotationItemList, index, selectedPromotion);
            }
            else {
                if (helper.hasMixMatch(selectedPromotion)) {
                    helper.updateThresholdItemMixMatch(quotationItemList, index, selectedPromotion);
                }
                else {
                    helper.updateThresholdItem(quotationItemList, index, selectedPromotion);
                }
            }
        }
        helper.addOfferingItems(quotationItemList, index, selectedPromotion, indexObj, component);
        console.log("controller 1458 RUN addOffering");
        console.log("controller 1458 SHOWiTEM", quotationItemList);

        helper.checkMeetOfferingPoolLimit(quotationItemList, index, selectedPromotion);

        helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
        helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

        console.log("controller 1467 SHOWiTEM", quotationItemList);
        component.set('v.orderItemList', []);
        component.set('v.orderItemList', quotationItemList);
        helper.calculateTotal(component);

        component.set('v.oldSelectedThresholdProducts', []);
        component.set('v.oldSelectedOfferingProducts', []);

        component.set('v.editRow', -1);
    },
    handleOfferingSelection: function (component, event, helper) {
        var item = event.currentTarget;
        if (item && item.dataset) {
            var value = item.dataset.value;
            var name = item.getAttribute('data-name');
            var selected = item.dataset.selected;
            var options = component.get("v.offeringOpts");
            options.forEach(function (element) {
                var offering = element.offering;
                var products = element.products;
                if (offering.Name == name) {
                    products.forEach(function (p) {
                        if (p.Product__r.SF_Description__c == value) {
                            p.selected = p.selected ? false : true;
                        }
                    });
                }
            });
            component.set("v.offeringOpts", options);
        }
    },
    handleReSelectProductClick: function (component, event, helper) {
        var index = component.get('v.operationRow');
        var quotationItemList = component.get('v.orderItemList');
        var quotationItem = quotationItemList[index];
        var selectedPromotion = quotationItem.Promotion;
        component.set('v.selectedPromotion', selectedPromotion);
        if (helper.hasMixMatch(selectedPromotion)) {
            var thresholdOpts = [];
            var thresholdList = selectedPromotion.ruleList[0].thresholdList;
            var selectedThresholdProducts = [];
            thresholdList.forEach(function (tItem) {
                if (tItem.threshold.RecordType.DeveloperName == 'By_Mix_Match') {
                    thresholdOpts.push(tItem);

                    var products = tItem.products;
                    products.forEach(function (pItem) {
                        if (pItem.selected) {
                            selectedThresholdProducts.push(pItem.Product__r.ProductCode);
                        }
                        pItem.hidden = false;
                    });
                }
            });
            component.set('v.oldSelectedThresholdProducts', selectedThresholdProducts);
            var offeringOpts = [];
            var selectedOfferingProducts = [];
            selectedPromotion.ruleList[0].offeringList.forEach(function (oItem) {
                if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                    offeringOpts.push(oItem);
                    var products = oItem.products;
                    products.forEach(function (pItem) {
                        if (pItem.selected) {
                            selectedOfferingProducts.push(pItem.Product__r.ProductCode);
                        }
                        pItem.hidden = false;
                    });
                }
            });
            if (quotationItem.hasPool) {
                component.set('v.isPoolFreeGoods', true);
            } else {
                component.set('v.isPoolFreeGoods', false);
            }
            component.set('v.thresholdOpts', thresholdOpts);
            component.set('v.offeringOpts', offeringOpts);
            component.set('v.oldSelectedOfferingProducts', selectedOfferingProducts);
            component.set('v.showThresholdProduct', true);
        } else {
            var ruleItem = selectedPromotion.ruleList[0];
            var offeringOpts = [];
            var selectedOfferingProducts = [];
            if (selectedPromotion.promotion.Promotion_Type__c == 'Price Break') {
                var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, index, selectedPromotion);
                if (avilableRuleItem) {
                    ruleItem = avilableRuleItem;
                }
            }
            ruleItem.offeringList.forEach(function (oItem) {
                if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                    offeringOpts.push(oItem);

                    var products = oItem.products;
                    products.forEach(function (pItem) {
                        if (pItem.selected) {
                            selectedOfferingProducts.push(pItem.Product__r.ProductCode);
                        }
                        pItem.hidden = false;
                    });
                }
            });
            component.set('v.offeringOpts', offeringOpts);
            component.set('v.oldSelectedOfferingProducts', selectedOfferingProducts);
            component.set("v.showOfferingProduct", true);
        }
    },
    backToEditOrder: function (component, event, helper) {
        component.set("v.showAvailablePromotions", false);
        component.set("v.showPromotionReminder", false);
    },
    nextToAvailablePromo: function (component, event, helper) {
        if (!helper.checkOrderQty(component)) {
            return;
        }
        if (!helper.checkTotalAmount(component)) {
            return;
        }
        var quotationItemList = component.get('v.orderItemList');
        //Yanko Start validate before click next
        for (var i = 0; i < quotationItemList.length; i++) {
            if (quotationItemList[i].counterror == true) {
                return;
            }

            var woqty = quotationItemList[i].Quantity__c;
            for (j = 0; j < quotationItemList.length; j++) {
                if (quotationItemList[j].ProductCode__c == quotationItemList[i].ProductCode__c) {
                    if (j != i) {
                        var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
                        var needcheck = 1;
                    }
                }
            }
            if (needcheck == 1) {
                var validateqty = Number(woqty) % Number(quotationItemList[i].CS_Exchange_Rate__c);
            } else {
                var validateqty = Number(quotationItemList[i].Quantity__c) % Number(quotationItemList[i].CS_Exchange_Rate__c);
            }

            if (validateqty != 0) {
                var needcheckqty = false;
                var notonlyfreegoods = false;
                for (var x = 0; x < quotationItemList.length; x++) {
                    if (quotationItemList[x].Unit_Price__c != 0 && quotationItemList[x].Product__c == quotationItemList[i].Product__c) {
                        var notonlyfreegoods = true;
                        if (quotationItemList[x].Promotion) {
                            if (quotationItemList[x].Promotion.ruleList) {
                                for (var b = 0; b <= quotationItemList[x].Promotion.ruleList.length; b++) {
                                    if (quotationItemList[x].Promotion.ruleList[b] && quotationItemList[x].Promotion.ruleList[b].thresholdList) {
                                        for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[b].thresholdList.length; j++) {
                                            if (quotationItemList[x].Promotion.ruleList[b].thresholdList[j] && quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products) {
                                                for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products.length; k++) {
                                                    if (quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products[k] && quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products[k].Product__c
                                                        && quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                        var multiplecontrol = quotationItemList[x].Promotion.ruleList[b].thresholdList[j].products[k].Multiple_Control__c;
                                                        // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                        if (multiplecontrol == 'Inner Box Multiple') {
                                                            var needcheckqty = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            var needcheckqty = true;
                        }
                    }
                }

                if (notonlyfreegoods == false) {
                    var needcheckfreegoodsqty = false;
                    for (var x = 0; x < quotationItemList.length; x++) {
                        if (quotationItemList[x].Unit_Price__c == 0.00 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[i].Product__c) {
                            if (quotationItemList[x].Promotion.ruleList) {
                                for (var b = 0; b <= quotationItemList[x].Promotion.ruleList.length; b++) {
                                    if (quotationItemList[x].Promotion.ruleList[b] && quotationItemList[x].Promotion.ruleList[b].offeringList) {
                                        for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[b].offeringList.length; j++) {
                                            if (quotationItemList[x].Promotion.ruleList[b].offeringList[j] && quotationItemList[x].Promotion.ruleList[b].offeringList[j].products) {
                                                for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[b].offeringList[j].products.length; k++) {
                                                    if (quotationItemList[x].Promotion.ruleList[b].offeringList[j].products[k] && quotationItemList[x].Promotion.ruleList[b].offeringList[j].products[k].Product__c
                                                        && quotationItemList[x].Promotion.ruleList[b].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                        var multiplecontrol = quotationItemList[x].Promotion.ruleList[b].offeringList[j].products[k].Multiple_Control__c;
                                                        // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                        if (multiplecontrol == 'Inner Box Multiple') {
                                                            var needcheckfreegoodsqty = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (needcheckfreegoodsqty == true) {
                        quotationItemList[i].counterror = true;
                        return;
                    }
                } else {
                    if (needcheckqty == true) {
                        quotationItemList[i].counterror = true;
                        return;
                    }
                }
            }
        }
        //Yanko End validate before click next

        //check promotions
        let promoCodes = new Set();
        quotationItemList.forEach(function (qItem) {
            if (qItem.PromotionName__c) {
                promoCodes.add(qItem.PromotionName__c);
            }
        });

        var termsPromo = component.get('v.termsPromo');
        if (termsPromo) {
            promoCodes.add(termsPromo.promotion.Promotion_Code_For_External__c);
        }

        var wholeOrderPromo = component.get('v.wholeOrderPromo');
        if (wholeOrderPromo) {
            promoCodes.add(wholeOrderPromo.promotion.Promotion_Code_For_External__c);
        }

        if (promoCodes.size == 0) {
            var notApplyPromoList = helper.initNotApplyPromoList(quotationItemList);
            if (notApplyPromoList.length > 0) {
                component.set("v.notApplyPromoList", notApplyPromoList);
                component.set("v.showAvailablePromotions", true);
            } else {
                var a = component.get('c.nextToPromoReminder');
                $A.enqueueAction(a);
            }
        } else {
            component.set('v.isBusy', true);
            var action = component.get("c.checkPromotions");
            action.setParams({
                "promotionCodes": Array.from(promoCodes),
                "customerId": null
            });
            action.setCallback(this, function (response) {
                var state = response.getState();
                console.log('state--->' + state);
                if (state === "SUCCESS") {
                    var results = response.getReturnValue();
                    if (results != null && results != undefined) {
                        var data = JSON.parse(results);
                        if (data != null && data != undefined) {
                            if (data.isSuccess) {
                                component.set('v.isBusy', false);
                                var notApplyPromoList = helper.initNotApplyPromoList(quotationItemList);
                                if (notApplyPromoList.length > 0) {
                                    component.set("v.notApplyPromoList", notApplyPromoList);
                                    component.set("v.showAvailablePromotions", true);
                                } else {
                                    var a = component.get('c.nextToPromoReminder');
                                    $A.enqueueAction(a);
                                }
                            } else {
                                var toastEvent = $A.get("e.force:showToast");
                                toastEvent.setParams({
                                    "title": $A.get("$Label.c.CCM_Portal_Warning"),
                                    "message": data.errorMsg,
                                    "type": "Warning"
                                });
                                toastEvent.fire();
                                component.set('v.isBusy', false);
                            }
                        }
                    }
                } else {
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            component.set('v.isBusy', false);
                            alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                        }
                    } else {
                        component.set('v.isBusy', false);
                        alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                    }
                }
            });
            $A.enqueueAction(action);
        }
    },
    nextToPromoReminder: function (component, event, helper) {
        component.set("v.showAvailablePromotions", false);
        var quotationItemList = component.get('v.orderItemList');
        var reminderPromoList = helper.initReminderPromoList(quotationItemList);
        if (reminderPromoList.length > 0) {
            component.set("v.reminderPromoList", reminderPromoList);
            component.set("v.showPromotionReminder", true);
        } else {
            helper.doSaveAction(component, event, false);
        }
    },
    nextToFinal: function (component, event, helper) {
        component.set("v.showAvailablePromotions", false);
        component.set("v.showPromotionReminder", false);
        helper.doSaveAction(component, event, false);
    },
    handleTermsPromoChange: function (component, event, helper) {
        var termsPromoCode = event.getSource().get('v.value');
        component.set("v.termsPromoCode", termsPromoCode);
    },
    onApplyTermsPromo: function (component, event, helper) {
        var termsPromoCode = component.get('v.termsPromoCode');
        if (termsPromoCode) {
            var action = component.get("c.getPromotion");
            action.setParams({
                "promotionCode": termsPromoCode,
                "customerId": component.get('v.customerId'),
                "isDropShip": false
            });
            action.setCallback(this, function (response) {
                var state = response.getState();
                if (state === "SUCCESS") {
                    var result = response.getReturnValue();
                    if (result) {
                        var data = JSON.parse(result);
                        if (data) {
                            var promoObj = data.promotion;
                            if (promoObj) {
                                var promoType = promoObj.Promotion_Type__c;
                                var quotationItemList = component.get('v.orderItemList');
                                if (promoType == 'Whole Order Promo') {
                                    var isMeetWholeOrderPromo = helper.isMeetWholeOrderPromo(quotationItemList, data);
                                    if (isMeetWholeOrderPromo && promoObj.Brands__c == component.get('v.brand')) {
                                        component.set("v.wholeOrderPromo", data);
                                        helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
                                        component.set('v.orderItemList', []);
                                        component.set('v.orderItemList', quotationItemList);
                                        helper.calculateTotal(component);
                                    } else {
                                        var toastEvent = $A.get("e.force:showToast");
                                        toastEvent.setParams({
                                            "title": $A.get("$Label.c.CCM_Portal_Warning"),
                                            "message": $A.get("$Label.c.CCM_Portal_DoNotMeetTheWholeOrderPromotionRule"),
                                            "type": "Warning"
                                        });
                                        toastEvent.fire();
                                        //alert('Error: Do Not Meet The Whole Order Promotion Rule!');
                                    }
                                } else if (promoType == 'Payment Term Promo') {
                                    var isMeetPaymentTermPromo = helper.isMeetPaymentTermPromo(quotationItemList, data);
                                    if (isMeetPaymentTermPromo && promoObj.Brands__c == component.get('v.brand')) {
                                        component.set("v.termsPromo", data);
                                        var termKey = data.ruleList[0].offeringList[0].offering.Payment_Term__c;
                                        var termLabel = data.ruleList[0].offeringList[0].offering.Payment_Term_Label__c;
                                        component.set("v.paymentTermLabel", termLabel.substring(termKey.length + 3));
                                        component.set('v.paymentTermValue', termKey);
                                        component.set('v.needResearch', false);
                                    } else {
                                        var toastEvent = $A.get("e.force:showToast");
                                        toastEvent.setParams({
                                            "title": $A.get("$Label.c.CCM_Portal_Warning"),
                                            "message": $A.get("$Label.c.CCM_Portal_DoNotMeetTheWholeOrderPromotionRule"),
                                            "type": "Warning"
                                        });
                                        toastEvent.fire();
                                        //alert('Error: Do Not Meet The Payment Term Promotion Rule!');
                                    }
                                }
                            }
                        }
                    } else {
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": $A.get("$Label.c.CCM_Portal_Warning"),
                            "message": $A.get("$Label.c.CCM_Portal_InvaildPromotion"),
                            "type": "Warning"
                        });
                        toastEvent.fire();
                        //alert("ERROR: Invaild Promotion.");
                    }
                } else {
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            alert($A.get("$Label.c.CCM_Portal_Error") + ': ' + errors[0].message);
                        }
                    } else {
                        alert($A.get("$Label.c.CCM_Portal_ErrorTips"));
                    }
                }
            });
            $A.enqueueAction(action);
            component.set("v.termsPromoCode", "");
        }
    },
    handleDeleteTermsPromo: function (component, event, helper) {
        component.set("v.termsPromo", "");
        helper.getPaymentTermRule(component);
    },
    handleDeleteWholeOrderPromo: function (component, event, helper) {
        var orderItemList = component.get('v.orderItemList');
        helper.removeWholeOrderPromotionOfferingItem(orderItemList, component.get('v.wholeOrderPromo'), component);
        component.set("v.wholeOrderPromo", "");
        component.set('v.orderItemList', []);
        component.set('v.orderItemList', orderItemList);
        helper.calculateTotal(component);
    },
    searchMixMatchProducts: function (component, event, helper) {
        var thresholdOpts = component.get('v.thresholdOpts');
        var value = event.getSource().get('v.value').toUpperCase();
        var index = event.getSource().get('v.name');
        var tItem = thresholdOpts[index];
        var products = tItem.products;
        if (!value) {
            products.forEach(function (p) {
                p.hidden = false;
            });
        } else {
            products.forEach(function (p) {
                var pName = p.Product__r.SF_Description__c.toUpperCase();
                if (pName.indexOf(value) != -1) {
                    p.hidden = false;
                } else {
                    p.hidden = true;
                }
            });
        }
        component.set('v.thresholdOpts', thresholdOpts);
    },
    searchPoolFreeGoods: function (component, event, helper) {
        var offeringOpts = component.get('v.offeringOpts');
        var value = event.getSource().get('v.value').toUpperCase();
        var index = event.getSource().get('v.name');
        var oItem = offeringOpts[index];
        var products = oItem.products;
        if (!value) {
            products.forEach(function (p) {
                p.hidden = false;
            });
        } else {
            products.forEach(function (p) {
                var pName = p.Product__r.SF_Description__c.toUpperCase();
                if (pName.indexOf(value) != -1) {
                    p.hidden = false;
                } else {
                    p.hidden = true;
                }
            });
        }
        component.set('v.offeringOpts', offeringOpts);
    },
    refreshFreightFee: function (component, event, helper) {
        helper.calculateTotal(component);
    },
    //YankoStart
    handleReduceClick: function (component, event, helper) {
        console.log('handleReduceClickyyy');
        var quotationItemList = component.get('v.orderItemList');
        let _index = component.get('v.operationRow');
        var qItem = quotationItemList[_index];
        var exchangerate = qItem.CS_Exchange_Rate__c;
        var promquantity = quotationItemList[_index].Quantity__c;

        //for buy qty
        var buyqty = 1;
        var productBuyQty = 1;
        var productMinQty = 1;
        if(quotationItemList[_index].Promotion){
            if(quotationItemList[_index].Promotion.ruleList){
                for(var i=0; i<quotationItemList[_index].Promotion.ruleList.length; i++){
                    if(quotationItemList[_index].Promotion.ruleList[i] && quotationItemList[_index].Promotion.ruleList[i].thresholdList){
                        for(var j=0; j<quotationItemList[_index].Promotion.ruleList[i].thresholdList.length; j++){
                            //BUY QTY promotion如果是price Discount 或者 Price Break，查找Buy Qty
                            if(quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Discount" || quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Break"){
                                if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j]
                                    && ['By_Quantity_of_Specific_Product', 'By_Amount_of_Specific_Product'].includes(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName)
                                    && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[0].Product__c === quotationItemList[_index].Product__c
                                ){
                                    var buyqty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                }
                            }
                            // add haibo: 新增 Therthod 为By Quantity of Specific Product && product buyQty, 按 product buyQty成倍修改数量（应用promotion时）
                            for(var k=0; k<quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products.length; k++){
                                if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j]
                                    && (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'
                                    && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c)){
                                    if (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == qItem.Product__c) {
                                        if(quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Break"){
                                            let _productMinQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                            if(_productMinQty > productMinQty && productMinQty === 1){
                                                productMinQty = _productMinQty;
                                            }else if(_productMinQty < productMinQty){
                                                productMinQty = _productMinQty;
                                            }
                                        }else{
                                            productMinQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                        }
                                        console.log(JSON.stringify(quotationItemList[_index]), 'quotationItemList[_index]===============');
                                        // 根据promotion type获取不同的buy qty
                                        if (quotationItemList[_index].Promotion.promotion.Promotion_Type__c == 'Price Discount') {
                                            productBuyQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                        } else {
                                            productBuyQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        // add haibo
        console.log(productBuyQty, 'productBuyQty====================');
        console.log(_index, 'index====================');
        console.log(JSON.stringify(quotationItemList), 'quotationItemList====================');
        console.log(quotationItemList[_index].ProductCode__c, 'ProductCode__c====================');

        for(i=0;i<quotationItemList.length;i++){
            if(quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c){
                console.log(i, 'i==================');
                if(i != _index){
                    console.log(i, _index, 'i != _index==================');
                    var promquantity = Number(promquantity) + Number(quotationItemList[i].Quantity__c);
                    var needcheck = 1;
                }
            }
        }
        if (needcheck == 1) {
            console.log('needcheck================');
            console.log(quotationItemList[_index].Quantity__c, 'quotationItemList[_index].Quantity__c================');
            console.log(productBuyQty, 'productBuyQty====================');
            console.log(productMinQty, 'productMinQty====================');
            if (quotationItemList[_index].Quantity__c > 1) {
                // add haibo
                if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
                    if(quotationItemList[_index].Quantity__c > productBuyQty){
                        quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(productBuyQty);
                        promquantity = Number(promquantity) - Number(productBuyQty);
                        // 判断 promquantity是否小于Minqty
                        if(quotationItemList[_index].Quantity__c < productMinQty){
                            var toastEvent = $A.get("e.force:showToast");
                            toastEvent.setParams({
                                "title": "Warning!",
                                "message": "Qty(EA) cannot be further reduced. ",
                                "type": "Warning"
                            });
                            toastEvent.fire();
                            // productBuyQty向上取整
                            quotationItemList[_index].Quantity__c = productMinQty;
                        }
                    }else{
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Warning!",
                            "message": "Qty(EA) cannot be further reduced. ",
                            "type": "Warning"
                        });
                        toastEvent.fire();
                    }
                }
                else if(buyqty && buyqty != 0 && buyqty != 1 && productBuyQty == 1){
                    //for buyqty
                    if(quotationItemList[_index].Quantity__c > buyqty){
                        if(quotationItemList[_index].Quantity__c % Number(buyqty) != 0){
                            for(var i = buyqty; i > 0; i--){
                                if((Number(quotationItemList[_index].Quantity__c) - i) % Number(buyqty) == 0){
                                    quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - i;
                                    promquantity = Number(promquantity) - i;
                                }
                            }
                        }else{
                            quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(buyqty);
                            promquantity = Number(promquantity) - Number(buyqty);
                        }
                    }else{
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Warning!",
                            "message": "Qty(EA) cannot be further reduced. ",
                            "type": "Warning"
                        });
                        toastEvent.fire();
                    }
                }else{
                    quotationItemList[_index].Quantity__c = quotationItemList[_index].Quantity__c - 1;
                    promquantity = Number(promquantity) - 1;
                }
                var promremainder = Number(promquantity) % Number(exchangerate);
                if (promremainder != 0) {
                    var needcheckqty = false;
                    var notonlyfreegoods = false;
                    for (var x = 0; x < quotationItemList.length; x++) {
                        if (quotationItemList[x].Unit_Price__c != 0 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
                            var notonlyfreegoods = true;
                            if (quotationItemList[x].Promotion) {
                                if (quotationItemList[x].Promotion.ruleList) {
                                    for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                        if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
                                            for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
                                                if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
                                                    for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
                                                        if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                            && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                            var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                            // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                            if (multiplecontrol == 'Inner Box Multiple') {
                                                                var needcheckqty = true;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                var needcheckqty = true;
                            }
                        }
                    }

                    if (notonlyfreegoods == false) {
                        var needcheckfreegoodsqty = false;
                        for (var x = 0; x < quotationItemList.length; x++) {
                            if (quotationItemList[x].Unit_Price__c == 0.00 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
                                if (quotationItemList[x].Promotion.ruleList) {
                                    for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                        if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
                                            for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
                                                if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
                                                    for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
                                                        if (quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                            && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                            var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                            // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                            if (multiplecontrol == 'Inner Box Multiple') {
                                                                var needcheckfreegoodsqty = true;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (needcheckfreegoodsqty == true) {
                            for (var i = 0; i < quotationItemList.length; i++) {
                                if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
                                    quotationItemList[i].counterror = true;
                                }
                            }
                        }
                    } else {
                        if (needcheckqty == true) {
                            for (var i = 0; i < quotationItemList.length; i++) {
                                if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
                                    quotationItemList[i].counterror = true;
                                }
                            }
                        }
                    }
                } else {
                    component.set('v.showcaseqtyalert', false);
                    quotationItemList[_index].counterror = false;
                    for (var i = 0; i < quotationItemList.length; i++) {
                        if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
                            quotationItemList[i].counterror = false;
                        }
                    }
                }
            } else {
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": $A.get("$Label.c.CCM_Portal_Warning"),
                    "message": $A.get("$Label.c.CCM_Portal_QtyEAcannotbefurtherreduced"),
                    "type": "Warning"
                });
                toastEvent.fire();
            }
        } else {
            console.log('no needcheck================');
            console.log(quotationItemList[_index].Quantity__c, 'quotationItemList[_index].Quantity__c================');
            console.log(productBuyQty, 'productBuyQty====================');
            console.log(productMinQty, 'productMinQty====================');
            // add haibo
            if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
                if(quotationItemList[_index].Quantity__c > productBuyQty){
                    quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(productBuyQty);
                    promquantity = Number(promquantity) - Number(productBuyQty);
                    // 判断 promquantity是否小于Minqty
                    if(quotationItemList[_index].Quantity__c < productMinQty){
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": $A.get("$Label.c.CCM_Portal_Warning"),
                            "message": $A.get("$Label.c.CCM_Portal_QtyEAcannotbefurtherreduced"),
                            "type": "Warning"
                        });
                        toastEvent.fire();
                        // productBuyQty向上取整
                        quotationItemList[_index].Quantity__c = productMinQty;
                    }
                }else{
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Warning"),
                        "message": $A.get("$Label.c.CCM_Portal_QtyEAcannotbefurtherreduced"),
                        "type": "Warning"
                    });
                    toastEvent.fire();
                }
            }
            else if(buyqty && buyqty != 0 && buyqty != 1 && productBuyQty == 1){
                //for buyqty
                if(quotationItemList[_index].Quantity__c > buyqty){
                    if(quotationItemList[_index].Quantity__c % Number(buyqty) != 0){
                        for(var i = buyqty; i > 0; i--){
                            if((Number(quotationItemList[_index].Quantity__c) - i) % Number(buyqty) == 0){
                                quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - i;
                            }
                        }
                    }else{
                        quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(buyqty);
                    }

                    if(quotationItemList[_index].Quantity__c % Number(quotationItemList[_index].CS_Exchange_Rate__c) != 0){
                        var needcheckqty = false;
                        var notonlyfreegoods = false;
                        for(var x=0; x<quotationItemList.length; x++){
                            if(quotationItemList[x].Unit_Price__c != 0.00 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c){
                                var notonlyfreegoods = true;
                                if(quotationItemList[x].Promotion){
                                    if(quotationItemList[x].Promotion.ruleList){
                                        for(var i=0; i<=quotationItemList[x].Promotion.ruleList.length; i++){
                                            if(quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList){
                                                for(var j=0; j<=quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++){
                                                    if(quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products){
                                                        for(var k=0; k<=quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++){
                                                            if(quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                                && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c){
                                                                var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                                // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                                if(multiplecontrol == 'Inner Box Multiple'){
                                                                    var needcheckqty = true;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }else{
                                    var needcheckqty = true;
                                }
                            }
                        }

                        if(notonlyfreegoods == false){
                            var needcheckfreegoodsqty = false;
                            for(var x=0; x<quotationItemList.length; x++){
                                if(quotationItemList[x].Unit_Price__c == 0.00 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[_index].Product__c){
                                    if(quotationItemList[x].Promotion.ruleList){
                                        for(var i=0; i<=quotationItemList[x].Promotion.ruleList.length; i++){
                                            if(quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList){
                                                for(var j=0; j<=quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++){
                                                    if(quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products){
                                                        for(var k=0; k<=quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++){
                                                            if(quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                                && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c){
                                                                var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                                // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                                if(multiplecontrol == 'Inner Box Multiple'){
                                                                    var needcheckfreegoodsqty = true;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            if(needcheckfreegoodsqty == true){
                                for(var i=0; i<quotationItemList.length; i++){
                                    if(quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c){
                                        quotationItemList[i].counterror = true;
                                    }
                                }
                            }
                        }else{
                            if(needcheckqty == true){
                                for(var i=0; i<quotationItemList.length; i++){
                                    if(quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c){
                                        quotationItemList[i].counterror = true;
                                    }
                                }
                            }
                        }
                    }else{
                        quotationItemList[_index].counterror = false;
                        for(var i=0; i<quotationItemList.length; i++){
                            if(quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c){
                                quotationItemList[i].counterror = false;
                            }
                        }
                    }

                }else{
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Warning"),
                        "message": $A.get("$Label.c.CCM_Portal_QtyEAcannotbefurtherreduced"),
                        "type": "Warning"
                    });
                    toastEvent.fire();
                }
            } else {
                if (quotationItemList[_index].Quantity__c > quotationItemList[_index].CS_Exchange_Rate__c) {
                    if (quotationItemList[_index].Quantity__c % quotationItemList[_index].CS_Exchange_Rate__c != 0) {
                        for (var i = quotationItemList[_index].Quantity__c; i > quotationItemList[_index].Quantity__c - quotationItemList[_index].CS_Exchange_Rate__c; i--) {
                            if (i % quotationItemList[_index].CS_Exchange_Rate__c == 0) {
                                quotationItemList[_index].Quantity__c = i;
                            }
                        }
                    } else {
                        quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) - Number(quotationItemList[_index].CS_Exchange_Rate__c);
                    }

                    component.set('v.showcaseqtyalert', false);
                    quotationItemList[_index].counterror = false;
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": $A.get("$Label.c.CCM_Portal_Warning"),
                        "message": $A.get("$Label.c.CCM_Portal_QtyEAcannotbefurtherreduced"),
                        "type": "Warning"
                    });
                    toastEvent.fire();
                }
            }
        }
        var unitPrice = quotationItemList[_index].List_Price__c;
        var subtotal = Number(unitPrice) * Number(quotationItemList[_index].Quantity__c);
        quotationItemList[_index].Sub_Total__c = subtotal.toFixed(2);
        // change0227 没有promotion情况下 加减会错误更新subtotal
        if (quotationItemList[_index].Whole_Order_Promotion__c) {
            helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
            // quotationItemList[_index].Sub_Total__c = Number(quotationItemList[_index].Sub_Total__c) + Number(quotationItemList[_index].Whole_Order_Promo_Discount_Amount__c);
        }
        helper.calculateTotal(component);
        component.set('v.orderItemList', quotationItemList);

        if (quotationItemList[_index].Promotion) {
            var promotion = quotationItemList[_index].Promotion;
            var hasRemoveItem = false;
            var hasPopup = false;
            var ruleName = quotationItemList[_index].Promotion_Rule_Name__c;
            if (promotion && (quotationItemList[_index].HasPromo || quotationItemList[_index].isThreshold)) {
                var isMeetThreshold = helper.isMeetThreshold(quotationItemList, _index, promotion);
                if (promotion.promotion.Promotion_Type__c == 'BOGO'
                    || promotion.promotion.Promotion_Type__c == 'Full Pallet Promo') {
                    if (isMeetThreshold) {
                        helper.updateThresholdItem(quotationItemList, _index, promotion);
                        helper.updateOfferingItems(quotationItemList, _index, promotion);
                    } else {
                        var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
                        helper.removePromotionQuotationItem(quotationItemList, _index, component);
                        helper.clearPromotionStatus(quotationItemList, initialIndex);
                        hasRemoveItem = true;
                    }
                } else if (promotion.promotion.Promotion_Type__c == 'Price Break') {
                    if (isMeetThreshold) {
                        var oldRuleName = quotationItemList[_index].Promotion_Rule_Name__c;
                        var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, _index, promotion);
                        var newRuleName = avilableRuleItem.ruleName;
                        if (oldRuleName == newRuleName) {
                            helper.updateThresholdItem(quotationItemList, _index, promotion);
                            helper.updateOfferingItems(quotationItemList, _index, promotion);
                        } else {
                            if (helper.isPoolFreeGoods(avilableRuleItem)) {
                                helper.clearSelectedProducts(promotion);
                                var offeringOpts = [];
                                avilableRuleItem.offeringList.forEach(function (oItem) {
                                    if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                                        offeringOpts.push(oItem);
                                    }
                                });

                                quotationItemList[_index].hasPool = true;
                                component.set('v.offeringOpts', offeringOpts);
                                component.set('v.selectedPromotion', promotion);
                                component.set("v.showOfferingProduct", true);
                                hasPopup = true;
                                component.set('v.editRow', _index);
                                return;
                            } else {
                                helper.removePromotionQuotationItem(quotationItemList, _index, component);
                                var indexObj = {
                                    "startindex": 0
                                };
                                quotationItemList[_index].hasPool = false;
                                helper.addThresholdItems(quotationItemList, _index, indexObj, component);
                                helper.updateThresholdItem(quotationItemList, _index, promotion);
                                helper.addOfferingItems(quotationItemList, _index, promotion, indexObj, component);
                                hasRemoveItem = true;
                            }
                        }
                    } else {
                        helper.removePromotionQuotationItem(quotationItemList, _index, component);
                        helper.clearPromotionStatus(quotationItemList, _index);
                        hasRemoveItem = true;
                    }
                } else if (promotion.promotion.Promotion_Type__c == 'Price Discount') {
                    if (isMeetThreshold) {
                        helper.updateThresholdItem(quotationItemList, _index, promotion);
                    } else {
                        var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
                        helper.removePromotionQuotationItem(quotationItemList, _index, component);
                        helper.clearPromotionStatus(quotationItemList, initialIndex);
                        hasRemoveItem = true;
                    }
                } else if (promotion.promotion.Promotion_Type__c == 'Mix & Match') {
                    if (isMeetThreshold) {
                        helper.updateThresholdItemMixMatch(quotationItemList, _index, promotion);
                        helper.updateOfferingItems(quotationItemList, _index, promotion);
                    } else {
                        quotationItemList.forEach(function (qItem) {
                            if (qItem.Promotion_Rule_Name__c == ruleName
                                && qItem.isMix) {
                                qItem.isMeet = false;
                                qItem.Promo_Discount_Amount__c = 0.00;
                                if (qItem.Quantity__c && qItem.Quantity__c > 0) {
                                    qItem.Sub_Total__c = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Discount_Amount__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Whole_Order_Promo_Discount_Amount__c);
                                    qItem.Unit_Price__c = Number((qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2));
                                } else {
                                    qItem.Sub_Total__c = 0.00;
                                    qItem.Unit_Price__c = qItem.List_Price__c;
                                }
                            }
                        });
                        helper.removeOfferingItemsOutOfPool(quotationItemList, _index, promotion);
                    }
                } else if (promotion.promotion.Promotion_Type__c == 'Others') {
                    if (isMeetThreshold) {
                        if (helper.hasMixMatch(promotion)) {
                            helper.updateThresholdItemMixMatch(quotationItemList, _index, promotion);
                        }
                        else {
                            helper.updateThresholdItem(quotationItemList, _index, promotion);
                        }
                        helper.updateOfferingItems(quotationItemList, _index, promotion);
                    } else {
                        if (helper.hasMixMatch(promotion)) {
                            quotationItemList.forEach(function (qItem) {
                                if (qItem.Promotion_Rule_Name__c == ruleName
                                    && (qItem.Is_Initial__c || qItem.isThreshold)) {
                                    qItem.Promo_Discount_Amount__c = 0.00;
                                    if (qItem.Quantity__c && qItem.Quantity__c > 0) {
                                        qItem.Sub_Total__c = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Discount_Amount__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Whole_Order_Promo_Discount_Amount__c);
                                        qItem.Unit_Price__c = Number((qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2));
                                    } else {
                                        qItem.Sub_Total__c = 0.00;
                                        qItem.Unit_Price__c = qItem.List_Price__c;
                                    }
                                    if (qItem.isMix) {
                                        qItem.isMeet = false;
                                    }
                                }
                            });
                            helper.removeOfferingItemsOutOfPool(quotationItemList, _index, promotion);
                        } else {
                            var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
                            helper.removePromotionQuotationItem(quotationItemList, _index, component);
                            helper.clearPromotionStatus(quotationItemList, initialIndex);
                            hasRemoveItem = true;
                        }
                    }
                }
            } else if (promotion && quotationItemList[_index].isOffering && quotationItemList[_index].isPool) {
                if (quotationItemList[_index].Promotion__c == quotationItemList[_index].Whole_Order_Promotion__c) {
                    quotationItemList[_index].Whole_Order_Promo_Discount_Amount__c = Number(quotationItemList[_index].List_Price__c * quotationItemList[_index].Quantity__c) * -1;
                } else {
                    quotationItemList[_index].Promo_Discount_Amount__c = Number(quotationItemList[_index].List_Price__c * quotationItemList[_index].Quantity__c) * -1;
                }

                quotationItemList[_index].Sub_Total__c = 0.00;
            }

            if (hasPopup) {
                return;
            }
            if (!hasRemoveItem) {
                if (promotion && quotationItemList[_index].Promotion) {
                    var ruleItem = promotion.ruleList[0];
                    if (promotion.promotion.Promotion_Type__c == 'Price Break') {
                        var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, _index, promotion);
                        if (avilableRuleItem) {
                            ruleItem = avilableRuleItem;
                        }
                    }
                    if (quotationItemList[_index].Promotion__c != quotationItemList[_index].Whole_Order_Promotion__c) {
                        if (helper.isPoolFreeGoods(ruleItem)) {
                            helper.checkMeetOfferingPoolLimit(quotationItemList, _index, promotion);
                        }
                    } else {
                        if (helper.isPoolFreeGoods(ruleItem)) {
                            helper.checkMeetWholeOrderPromoOfferingPoolLimit(quotationItemList, component);
                            component.set('v.orderItemList', quotationItemList);
                            helper.calculateTotal(component);
                            return;
                        }
                    }
                }
            }

            helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
            helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

            if (hasRemoveItem) {
                component.set('v.orderItemList', []);
            }
        }

        // 24.10.23: 修复移除了不符合Promotion Rule的产品后，执行到此行报错。
        if (quotationItemList[_index] && quotationItemList[_index].counterror == false) {
            for (var s = 0; s < quotationItemList.length; s++) {
                var woqty = quotationItemList[s].Quantity__c;
                for (j = 0; j < quotationItemList.length; j++) {
                    if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
                        if (j != s) {
                            var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
                            var needcheck = 1;
                        }
                    }
                }
                if (needcheck == 1) {
                    var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
                } else {
                    var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
                }

                if (validateqty != 0) {
                    var needcheckqty = false;
                    var notonlyfreegoods = false;
                    for (var x = 0; x < quotationItemList.length; x++) {
                        if (quotationItemList[x].Unit_Price__c != 0 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
                            var notonlyfreegoods = true;
                            if (quotationItemList[x].Promotion) {
                                if (quotationItemList[x].Promotion.ruleList) {
                                    for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                        if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
                                            for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
                                                if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
                                                    for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
                                                        if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                            && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                            var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                            // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                            if (multiplecontrol == 'Inner Box Multiple') {
                                                                var needcheckqty = true;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                var needcheckqty = true;
                            }
                        }
                    }

                    if (notonlyfreegoods == false) {
                        var needcheckfreegoodsqty = false;
                        for (var x = 0; x < quotationItemList.length; x++) {
                            if (quotationItemList[x].Unit_Price__c == 0.00 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
                                if (quotationItemList[x].Promotion.ruleList) {
                                    for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                        if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
                                            for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
                                                if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
                                                    for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
                                                        if (quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                            && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                            var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                            // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                            if (multiplecontrol == 'Inner Box Multiple') {
                                                                var needcheckfreegoodsqty = true;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (needcheckfreegoodsqty == true) {
                            quotationItemList[s].counterror = true;
                        } else {
                            quotationItemList[s].counterror = false;
                        }
                    } else {
                        if (needcheckqty == true) {
                            quotationItemList[s].counterror = true;
                        } else {
                            quotationItemList[s].counterror = false;
                        }
                    }
                } else {
                    quotationItemList[s].counterror = false;
                }
            }
        }

        component.set('v.orderItemList', quotationItemList);
        helper.calculateTotal(component);
    },

    handleIncreaseClick: function (component, event, helper) {
        console.log('handleIncreaseClickyyy');
        var quotationItemList = component.get('v.orderItemList');
        let _index = component.get('v.operationRow');
        var qItem = quotationItemList[_index];
        var exchangerate = qItem.CS_Exchange_Rate__c;
        var promquantity = quotationItemList[_index].Quantity__c;
        // add haibo
        var buyqty = 1;
        var productBuyQty = 1;
        console.log(JSON.stringify(qItem), 'qItem================');

        if(quotationItemList[_index].Promotion){
            if(quotationItemList[_index].Promotion.ruleList){
                for(var i=0; i<quotationItemList[_index].Promotion.ruleList.length; i++){
                    if(quotationItemList[_index].Promotion.ruleList[i] && quotationItemList[_index].Promotion.ruleList[i].thresholdList){
                        console.log(JSON.stringify(quotationItemList[_index].Promotion.ruleList[i].thresholdList), 'thresholdList.products================');
                        for(var j=0; j<quotationItemList[_index].Promotion.ruleList[i].thresholdList.length; j++){
                            //点加号时判断promotion是否为FP，如果是，需要加fP的值
                            if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName == "By_Full_Pallet_Quantity_of_Specific_Product"){
                                if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j] && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products){
                                    for(var k=0; k<quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products.length; k++){
                                        if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__r.Full_Pallet_Quantity__c
                                        && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__r.Full_Pallet_Quantity__c != 0){
                                            var fullpalletqty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__r.Full_Pallet_Quantity__c;
                                            var fullpalletprom = true;
                                        }
                                    }
                                }
                            }
                            //BUY QTY promotion如果是price Discount 或者 Price Break，查找Buy Qty
                            if(quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Discount" || quotationItemList[_index].Promotion.promotion.Promotion_Type__c == "Price Break"){
                                if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j]
                                    && ['By_Quantity_of_Specific_Product', 'By_Amount_of_Specific_Product'].includes(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName)
                                    && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[0].Product__c === quotationItemList[_index].Product__c
                                ){
                                    var buyqty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                }
                            }
                            // add haibo: 新增 Therthod 为By Quantity of Specific Product && product buyQty, 按 product buyQty成倍修改数量（应用promotion时）

                            for(var k=0; k<quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products.length; k++){
                                if(quotationItemList[_index].Promotion.ruleList[i].thresholdList[j]
                                    && (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.RecordType.DeveloperName == 'By_Quantity_of_Specific_Product'
                                    && quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c)){
                                    if (quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == qItem.Product__c) {
                                        // 根据promotion type获取不同的buy qty
                                        if (quotationItemList[_index].Promotion.promotion.Promotion_Type__c == 'Price Discount') {
                                            productBuyQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].threshold.Buy_Qty__c;
                                        } else {
                                            productBuyQty = quotationItemList[_index].Promotion.ruleList[i].thresholdList[j].products[k].Increment_For_Free_Goods__c;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        // add haibo
        console.log(productBuyQty, 'productBuyQty====================');
        console.log('buyqty ->',buyqty);

        for (i = 0; i < quotationItemList.length; i++) {
            if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
                if (i != _index) {
                    var promquantity = Number(promquantity) + Number(quotationItemList[i].Quantity__c);
                    var needcheck = 1;
                }
            }
        }
        if (needcheck == 1) {
            console.log('needcheck================');
            if (fullpalletprom == true) {
                quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(fullpalletqty);
                promquantity = Number(promquantity) + Number(fullpalletqty);
            }
            // add haibo
            else if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
                quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(productBuyQty);
                promquantity = Number(promquantity) + Number(productBuyQty);
            }
            else if(buyqty && buyqty != 0 && buyqty != 1 && productBuyQty == 1){
                //for buyqty
                if(quotationItemList[_index].Quantity__c % Number(buyqty) != 0){
                    for(var i = 0; i < buyqty; i++){
                        if((Number(quotationItemList[_index].Quantity__c) + i) % Number(buyqty) == 0){
                            quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + i;
                            promquantity = Number(promquantity) + i;
                        }
                    }
                }else{
                    quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(buyqty);
                    promquantity = Number(promquantity) + Number(buyqty);
                }
            } else {
                quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + 1;
                promquantity = Number(promquantity) + 1;
            }
            var promremainder = Number(promquantity) % Number(exchangerate);
            if (promremainder != 0) {
                var needcheckqty = false;
                var notonlyfreegoods = false;
                for (var x = 0; x < quotationItemList.length; x++) {
                    if (quotationItemList[x].Unit_Price__c != 0.00 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
                        var notonlyfreegoods = true;
                        if (quotationItemList[x].Promotion) {
                            if (quotationItemList[x].Promotion.ruleList) {
                                for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                    if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
                                        for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
                                            if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
                                                for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
                                                    if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                        && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                        var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                        // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                        if (multiplecontrol == 'Inner Box Multiple') {
                                                            var needcheckqty = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            var needcheckqty = true;
                        }
                    }
                }

                if (notonlyfreegoods == false) {
                    var needcheckfreegoodsqty = false;
                    for (var x = 0; x < quotationItemList.length; x++) {
                        if (quotationItemList[x].Unit_Price__c == 0.00 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[_index].Product__c) {
                            if (quotationItemList[x].Promotion.ruleList) {
                                for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                    if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
                                        for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
                                            if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
                                                for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
                                                    if (quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                        && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                        var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                        // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                        if (multiplecontrol == 'Inner Box Multiple') {
                                                            var needcheckfreegoodsqty = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (needcheckfreegoodsqty == true) {
                        for (var i = 0; i < quotationItemList.length; i++) {
                            if (quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c) {
                                quotationItemList[i].counterror = true;
                            }
                        }
                    }
                } else {
                    if (needcheckqty == true) {
                        for (var i = 0; i < quotationItemList.length; i++) {
                            if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
                                quotationItemList[i].counterror = true;
                            }
                        }
                    }
                }
            } else {
                quotationItemList[_index].counterror = false;
                for (var i = 0; i < quotationItemList.length; i++) {
                    if (quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c) {
                        quotationItemList[i].counterror = false;
                    }
                }
            }
        } else {
            console.log('no needcheck================');
            console.log('handleIncreaseClick quotationItemList[_index].Quantity__c ->',quotationItemList[_index].Quantity__c);
            if(fullpalletprom == true){
                quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(fullpalletqty);
            }
            // add haibo
            else if (productBuyQty && productBuyQty != 0 && productBuyQty != 1) {
                quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(productBuyQty);
                promquantity = Number(promquantity) + Number(productBuyQty);
            }
            else if(buyqty && buyqty != 0 && buyqty != 1 && productBuyQty == 1){
                //for buyqty
                if(quotationItemList[_index].Quantity__c % Number(buyqty) != 0){
                    for(var i = 0; i < buyqty; i++){
                        if((Number(quotationItemList[_index].Quantity__c) + i) % Number(buyqty) == 0){
                            quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + i;
                        }
                    }
                }else{
                    quotationItemList[_index].Quantity__c = Number(quotationItemList[_index].Quantity__c) + Number(buyqty);
                }

                if(quotationItemList[_index].Quantity__c % Number(quotationItemList[_index].CS_Exchange_Rate__c) != 0){
                    var needcheckqty = false;
                    var notonlyfreegoods = false;
                    for(var x=0; x<quotationItemList.length; x++){
                        if(quotationItemList[x].Unit_Price__c != 0.00 && quotationItemList[x].Product__c == quotationItemList[_index].Product__c){
                            var notonlyfreegoods = true;
                            if(quotationItemList[x].Promotion){
                                if(quotationItemList[x].Promotion.ruleList){
                                    for(var i=0; i<=quotationItemList[x].Promotion.ruleList.length; i++){
                                        if(quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList){
                                            for(var j=0; j<=quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++){
                                                if(quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products){
                                                    for(var k=0; k<=quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++){
                                                        if(quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                            && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c){
                                                            var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                            // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                            if(multiplecontrol == 'Inner Box Multiple'){
                                                                var needcheckqty = true;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }else{
                                var needcheckqty = true;
                            }
                        }
                    }

                    if(notonlyfreegoods == false){
                        var needcheckfreegoodsqty = false;
                        for(var x=0; x<quotationItemList.length; x++){
                            if(quotationItemList[x].Unit_Price__c == 0.00 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[_index].Product__c){
                                if(quotationItemList[x].Promotion.ruleList){
                                    for(var i=0; i<=quotationItemList[x].Promotion.ruleList.length; i++){
                                        if(quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList){
                                            for(var j=0; j<=quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++){
                                                if(quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products){
                                                    for(var k=0; k<=quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++){
                                                        if(quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                            && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c){
                                                            var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                            // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                            if(multiplecontrol == 'Inner Box Multiple'){
                                                                var needcheckfreegoodsqty = true;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if(needcheckfreegoodsqty == true){
                            for(var i=0; i<quotationItemList.length; i++){
                                if(quotationItemList[i].ProductCode__c == quotationItemList[index].ProductCode__c){
                                    quotationItemList[i].counterror = true;
                                }
                            }
                        }
                    }else{
                        if(needcheckqty == true){
                            for(var i=0; i<quotationItemList.length; i++){
                                if(quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c){
                                    quotationItemList[i].counterror = true;
                                }
                            }
                        }
                    }
                }else{
                    quotationItemList[_index].counterror = false;
                    for(var i=0; i<quotationItemList.length; i++){
                        if(quotationItemList[i].ProductCode__c == quotationItemList[_index].ProductCode__c){
                            quotationItemList[i].counterror = false;
                        }
                    }
                }

            }else{
                if(qItem.Quantity__c % qItem.CS_Exchange_Rate__c != 0){
                    for(var i = qItem.Quantity__c; i < qItem.Quantity__c + qItem.CS_Exchange_Rate__c; i++){
                        if(i % qItem.CS_Exchange_Rate__c == 0){
                            qItem.Quantity__c = i;
                        }
                    }
                }else{
                    qItem.Quantity__c = Number(qItem.Quantity__c) + Number(qItem.CS_Exchange_Rate__c);
                }
                qItem.counterror = false;
            }
            component.set('v.showcaseqtyalert', false);
            console.log('quotationItemList[_index].Quantity__c -> ', quotationItemList[_index].Quantity__c);
        }

        var unitPrice = qItem.List_Price__c;
        var subtotal = Number(unitPrice) * Number(qItem.Quantity__c);
        qItem.Sub_Total__c = subtotal.toFixed(2);
        helper.calculateTotal(component);
        component.set('v.orderItemList', quotationItemList);

        // }else{
        if (quotationItemList[_index].Promotion) {
            var promotion = quotationItemList[_index].Promotion;
            var hasRemoveItem = false;
            var hasPopup = false;
            var ruleName = quotationItemList[_index].Promotion_Rule_Name__c;
            if (promotion && (quotationItemList[_index].HasPromo || quotationItemList[_index].isThreshold)) {
                console.log('freegoodserror = ', _index);
                var isMeetThreshold = helper.isMeetThreshold(quotationItemList, _index, promotion);
                if (promotion.promotion.Promotion_Type__c == 'BOGO'
                    || promotion.promotion.Promotion_Type__c == 'Full Pallet Promo') {
                    if (isMeetThreshold) {
                        helper.updateThresholdItem(quotationItemList, _index, promotion);
                        helper.updateOfferingItems(quotationItemList, _index, promotion);
                    } else {
                        var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
                        helper.removePromotionQuotationItem(quotationItemList, _index, component);
                        helper.clearPromotionStatus(quotationItemList, initialIndex);
                        hasRemoveItem = true;
                    }
                } else if (promotion.promotion.Promotion_Type__c == 'Price Break') {
                    if (isMeetThreshold) {
                        var oldRuleName = quotationItemList[_index].Promotion_Rule_Name__c;
                        var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, _index, promotion);
                        var newRuleName = avilableRuleItem.ruleName;
                        if (oldRuleName == newRuleName) {
                            helper.updateThresholdItem(quotationItemList, _index, promotion);
                            helper.updateOfferingItems(quotationItemList, _index, promotion);
                        } else {
                            if (helper.isPoolFreeGoods(avilableRuleItem)) {
                                helper.clearSelectedProducts(promotion);
                                var offeringOpts = [];
                                avilableRuleItem.offeringList.forEach(function (oItem) {
                                    if (oItem.offering.RecordType.DeveloperName == 'Pool_of_Free_Goods_of_Customer_Choice') {
                                        offeringOpts.push(oItem);
                                    }
                                });

                                quotationItemList[_index].hasPool = true;
                                component.set('v.offeringOpts', offeringOpts);
                                component.set('v.selectedPromotion', promotion);
                                component.set("v.showOfferingProduct", true);
                                hasPopup = true;
                                component.set('v.editRow', _index);
                                return;
                            } else {
                                helper.removePromotionQuotationItem(quotationItemList, _index, component);
                                var indexObj = {
                                    "startindex": 0
                                };
                                quotationItemList[_index].hasPool = false;
                                helper.addThresholdItems(quotationItemList, _index, indexObj, component);
                                helper.updateThresholdItem(quotationItemList, _index, promotion);
                                helper.addOfferingItems(quotationItemList, _index, promotion, indexObj, component);
                                hasRemoveItem = true;
                            }
                        }
                    } else {
                        helper.removePromotionQuotationItem(quotationItemList, _index, component);
                        helper.clearPromotionStatus(quotationItemList, _index);
                        hasRemoveItem = true;
                    }
                } else if (promotion.promotion.Promotion_Type__c == 'Price Discount') {
                    if (isMeetThreshold) {
                        helper.updateThresholdItem(quotationItemList, _index, promotion);
                    } else {
                        var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
                        helper.removePromotionQuotationItem(quotationItemList, _index, component);
                        helper.clearPromotionStatus(quotationItemList, initialIndex);
                        hasRemoveItem = true;
                    }
                } else if (promotion.promotion.Promotion_Type__c == 'Mix & Match') {
                    if (isMeetThreshold) {
                        helper.updateThresholdItemMixMatch(quotationItemList, _index, promotion);
                        helper.updateOfferingItems(quotationItemList, _index, promotion);
                    } else {
                        quotationItemList.forEach(function (qItem) {
                            if (qItem.Promotion_Rule_Name__c == ruleName
                                && qItem.isMix) {
                                qItem.isMeet = false;
                                qItem.Promo_Discount_Amount__c = 0.00;
                                if (qItem.Quantity__c && qItem.Quantity__c > 0) {
                                    qItem.Sub_Total__c = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Discount_Amount__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Whole_Order_Promo_Discount_Amount__c);
                                    qItem.Unit_Price__c = Number((qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2));
                                } else {
                                    qItem.Sub_Total__c = 0.00;
                                    qItem.Unit_Price__c = qItem.List_Price__c;
                                }
                            }
                        });
                        helper.removeOfferingItemsOutOfPool(quotationItemList, _index, promotion);
                    }
                } else if (promotion.promotion.Promotion_Type__c == 'Others') {
                    if (isMeetThreshold) {
                        if (helper.hasMixMatch(promotion)) {
                            helper.updateThresholdItemMixMatch(quotationItemList, _index, promotion);
                        }
                        else {
                            helper.updateThresholdItem(quotationItemList, _index, promotion);
                        }
                        helper.updateOfferingItems(quotationItemList, _index, promotion);
                    } else {
                        if (helper.hasMixMatch(promotion)) {
                            quotationItemList.forEach(function (qItem) {
                                if (qItem.Promotion_Rule_Name__c == ruleName
                                    && (qItem.Is_Initial__c || qItem.isThreshold)) {
                                    qItem.Promo_Discount_Amount__c = 0.00;
                                    if (qItem.Quantity__c && qItem.Quantity__c > 0) {
                                        qItem.Sub_Total__c = Number(qItem.List_Price__c * qItem.Quantity__c) + Number(qItem.Discount_Amount__c) + Number(qItem.Promo_Discount_Amount__c) + Number(qItem.Whole_Order_Promo_Discount_Amount__c);
                                        qItem.Unit_Price__c = Number((qItem.Sub_Total__c / qItem.Quantity__c).toFixed(2));
                                    } else {
                                        qItem.Sub_Total__c = 0.00;
                                        qItem.Unit_Price__c = qItem.List_Price__c;
                                    }
                                    if (qItem.isMix) {
                                        qItem.isMeet = false;
                                    }
                                }
                            });
                            helper.removeOfferingItemsOutOfPool(quotationItemList, _index, promotion);
                        } else {
                            var initialIndex = helper.getInitailIndex(quotationItemList, ruleName);
                            helper.removePromotionQuotationItem(quotationItemList, _index, component);
                            helper.clearPromotionStatus(quotationItemList, initialIndex);
                            hasRemoveItem = true;
                        }
                    }
                }
            } else if (promotion && quotationItemList[_index].isOffering && quotationItemList[_index].isPool) {
                if (quotationItemList[_index].Promotion__c == quotationItemList[_index].Whole_Order_Promotion__c) {
                    quotationItemList[_index].Whole_Order_Promo_Discount_Amount__c = Number(quotationItemList[_index].List_Price__c * quotationItemList[_index].Quantity__c) * -1;
                } else {
                    quotationItemList[_index].Promo_Discount_Amount__c = Number(quotationItemList[_index].List_Price__c * quotationItemList[_index].Quantity__c) * -1;
                }

                quotationItemList[_index].Sub_Total__c = 0.00;
            }

            if (hasPopup) {
                return;
            }
            if (!hasRemoveItem) {
                if (promotion && quotationItemList[_index].Promotion) {
                    var ruleItem = promotion.ruleList[0];
                    if (promotion.promotion.Promotion_Type__c == 'Price Break') {
                        var avilableRuleItem = helper.getAvailableRuleInPriceBreak(quotationItemList, _index, promotion);
                        if (avilableRuleItem) {
                            ruleItem = avilableRuleItem;
                        }
                    }
                    if (quotationItemList[_index].Promotion__c != quotationItemList[_index].Whole_Order_Promotion__c) {
                        if (helper.isPoolFreeGoods(ruleItem)) {
                            helper.checkMeetOfferingPoolLimit(quotationItemList, _index, promotion);
                        }
                    } else {
                        if (helper.isPoolFreeGoods(ruleItem)) {
                            helper.checkMeetWholeOrderPromoOfferingPoolLimit(quotationItemList, component);
                            component.set('v.orderItemList', quotationItemList);
                            helper.calculateTotal(component);
                            return;
                        }
                    }
                }
            }

            helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
            helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

            if (hasRemoveItem) {
                component.set('v.orderItemList', []);
            }

            //YANKO CTRL V REDUCE

        }

        //change0207 qItem.meetalert = 1;  qItem.meetalertmodel = Number(intj) + 1;
        if (quotationItemList[_index].counterror == false) {
            for (var s = 0; s < quotationItemList.length; s++) {
                var woqty = quotationItemList[s].Quantity__c;
                for (j = 0; j < quotationItemList.length; j++) {
                    if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
                        if (j != s) {
                            var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
                            var needcheck = 1;
                        }
                    }
                }
                if (needcheck == 1) {
                    var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
                } else {
                    var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
                }

                if (validateqty != 0) {
                    var needcheckqty = false;
                    var notonlyfreegoods = false;
                    for (var x = 0; x < quotationItemList.length; x++) {
                        if (quotationItemList[x].Unit_Price__c != 0.00 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
                            var notonlyfreegoods = true;
                            if (quotationItemList[x].Promotion) {
                                if (quotationItemList[x].Promotion.ruleList) {
                                    for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                        if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
                                            for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
                                                if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
                                                    for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
                                                        if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c
                                                            && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                            var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
                                                            // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                            if (multiplecontrol == 'Inner Box Multiple') {
                                                                var needcheckqty = true;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                var needcheckqty = true;
                            }
                        }
                    }

                    if (notonlyfreegoods == false) {
                        var needcheckfreegoodsqty = false;
                        for (var x = 0; x < quotationItemList.length; x++) {
                            if (quotationItemList[x].Unit_Price__c == 0.00 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
                                if (quotationItemList[x].Promotion.ruleList) {
                                    for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
                                        if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
                                            for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
                                                if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
                                                    for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
                                                        if (quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c
                                                            && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c) {
                                                            var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
                                                            // var meetminqty = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Minimum_Quantity__c;
                                                            if (multiplecontrol == 'Inner Box Multiple') {
                                                                var needcheckfreegoodsqty = true;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (needcheckfreegoodsqty == true) {
                            quotationItemList[s].counterror = true;
                        } else {
                            quotationItemList[s].counterror = false;
                        }
                    } else {
                        if (needcheckqty == true) {
                            quotationItemList[s].counterror = true;
                        } else {
                            quotationItemList[s].counterror = false;
                        }
                    }
                } else {
                    quotationItemList[s].counterror = false;
                }
            }
        }

        component.set('v.orderItemList', quotationItemList);
        helper.calculateTotal(component);
    },
    handleUploadFinish: function (component, event, helper) {
		component.set("v.isBusy", true);
		let productInfos = event.getParam("data");
		console.log(productInfos);
		productInfos = JSON.parse(JSON.stringify(productInfos));
		let shipDate = component.get("v.quotation.Expected_Delivery_Date__c");
		let quotationItemList = component.get("v.orderItemList");
		let ExpirateDate = null;
		productInfos.forEach((item) => {
			let data = item.InitData;
			let quotationItem = {};
			let expirateDate = data.ExpirateDate;
			ExpirateDate = data.ExpirateDate;
			let orgCode = data.OrgCode;
			component.set("v.ExpirateDate", expirateDate);
			component.set("v.OrgCode", orgCode);

			if (shipDate) {
				quotationItem["Ship_Date__c"] = shipDate;
			}

			if (data.product) {
				if (data.product.CS_Exchange_Rate__c && orgCode != "CCA") {
					quotationItem["CS_Exchange_Rate__c"] = data.product.CS_Exchange_Rate__c;
				} else {
					quotationItem["CS_Exchange_Rate__c"] = 1;
				}
				quotationItem["Quantity__c"] = item.Quantity;
			} else {
				quotationItem["CS_Exchange_Rate__c"] = 1;
				quotationItem["Quantity__c"] = 1;
			}
			quotationItem["Product__c"] = item.ProductId;
			quotationItem["Product__r"] = {};
			quotationItem["Product__r"]["SF_Description__c"] = data.product.SF_Description__c;
			quotationItem["ExpirateDate"] = data.ExpirateDate;
			quotationItem["Brand__c"] = data.product.Brand_Name__c;
			quotationItem["ProductCode__c"] = data.product.ProductCode;
			quotationItem["Gross_Weight__c"] = data.product.Weight__c == undefined ? 0.0 : data.product.Weight__c;
			quotationItem["Is_Over_Size_Product__c"] = data.product.OverSize__c;

			quotationItem["ProductCode__c"] = item.ProductCode;
			quotationItem["Brand__c"] = data.product.Brand_Name__c;

			var unitPrice = 0.0;
			if (data.priceBookEntry) {
				unitPrice = data.priceBookEntry.UnitPrice;
				quotationItem["Price_Book__c"] = data.priceBookEntry.Pricebook2Id;
			}

			if (unitPrice) {
				quotationItem["List_Price__c"] = unitPrice.toFixed(2);
				quotationItem["Unit_Price__c"] = unitPrice.toFixed(2);
				quotationItem["Sub_Total__c"] = (unitPrice * quotationItem["Quantity__c"]).toFixed(2);
			} else {
				quotationItem["List_Price__c"] = 0.0;
				quotationItem["Unit_Price__c"] = 0.0;
				quotationItem["Sub_Total__c"] = 0.0;
			}
			quotationItem["Discount_Amount__c"] = 0.0;
			quotationItem["Promo_Discount_Amount__c"] = 0.0;
			quotationItem["Whole_Order_Promo_Discount_Amount__c"] = 0.0;

			quotationItem["promotionList"] = data.promotionList;
			if (data.promotionList && data.promotionList.length > 0) {
				quotationItem["HasPromo"] = true;
				var promotionCodesStr = "";
				data.promotionList.forEach(function (pItem) {
					promotionCodesStr += "'" + pItem.promotion.Promo_Code__c + "',";
				});
				var strCodes = promotionCodesStr.slice(0, -1);
				quotationItem["promotionFilterCondition"] =
					'[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Open"},{"FieldName":"Promo_Code__c", "Condtion":"IN", "Value":"(' +
					strCodes +
					')"}]';
			} else {
				quotationItem["HasPromo"] = false;
				quotationItem["promotionFilterCondition"] =
					'[{"FieldName":"RecordType.DeveloperName","Condtion":"=","Value":"Sell_In_Promotion"},{"FieldName":"Promotion_Status__c","Condtion":"=","Value":"Error"}]';
			}
			quotationItem["Is_Initial__c"] = true;

			quotationItemList.push(quotationItem);
		});

		helper.checkWholeOrderPromotion(quotationItemList, component.get("v.wholeOrderPromo"), component);
		helper.checkPaymentTermPromotion(quotationItemList, component.get("v.termsPromo"), component);

		for (var s = 0; s < quotationItemList.length; s++) {
			var woqty = quotationItemList[s].Quantity__c;
			for (j = 0; j < quotationItemList.length; j++) {
				if (quotationItemList[j].ProductCode__c == quotationItemList[s].ProductCode__c) {
					if (j != s) {
						var woqty = Number(woqty) + Number(quotationItemList[j].Quantity__c);
						var needcheck = 1;
					}
				}
			}
			if (needcheck == 1) {
				var validateqty = Number(woqty) % Number(quotationItemList[s].CS_Exchange_Rate__c);
			} else {
				var validateqty = Number(quotationItemList[s].Quantity__c) % Number(quotationItemList[s].CS_Exchange_Rate__c);
			}

			if (validateqty != 0 && ExpirateDate == true) {
				var needcheckqty = false;
				var notonlyfreegoods = false;
				for (var x = 0; x < quotationItemList.length; x++) {
					if (quotationItemList[x].Unit_Price__c != 0.0 && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
						var notonlyfreegoods = true;
						if (quotationItemList[x].Promotion) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].thresholdList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].thresholdList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].thresholdList[j] && quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].thresholdList[j].products[k].Multiple_Control__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						} else {
							var needcheckqty = true;
						}
					}
				}

				if (notonlyfreegoods == false) {
					var needcheckfreegoodsqty = false;
					for (var x = 0; x < quotationItemList.length; x++) {
						if (quotationItemList[x].Unit_Price__c == 0.0 && quotationItemList[x].Promotion && quotationItemList[x].Product__c == quotationItemList[s].Product__c) {
							if (quotationItemList[x].Promotion.ruleList) {
								for (var i = 0; i <= quotationItemList[x].Promotion.ruleList.length; i++) {
									if (quotationItemList[x].Promotion.ruleList[i] && quotationItemList[x].Promotion.ruleList[i].offeringList) {
										for (var j = 0; j <= quotationItemList[x].Promotion.ruleList[i].offeringList.length; j++) {
											if (quotationItemList[x].Promotion.ruleList[i].offeringList[j] && quotationItemList[x].Promotion.ruleList[i].offeringList[j].products) {
												for (var k = 0; k <= quotationItemList[x].Promotion.ruleList[i].offeringList[j].products.length; k++) {
													if (
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k] &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c &&
														quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Product__c == quotationItemList[x].Product__c
													) {
														var multiplecontrol = quotationItemList[x].Promotion.ruleList[i].offeringList[j].products[k].Multiple_Control__c;
														if (multiplecontrol == "Inner Box Multiple") {
															var needcheckfreegoodsqty = true;
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					if (needcheckfreegoodsqty == true) {
						quotationItemList[s].counterror = true;
					} else {
						quotationItemList[s].counterror = false;
					}
				} else {
					if (needcheckqty == true) {
						quotationItemList[s].counterror = true;
					} else {
						quotationItemList[s].counterror = false;
					}
				}
			} else {
				quotationItemList[s].counterror = false;
			}
		}

		component.set("v.orderItemList", []);
		component.set("v.orderItemList", quotationItemList);
		helper.calculateTotal(component);
		helper.getPaymentTermRule(component);
		component.set("v.isBusy", false);
	}
})